<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Default Session Driver
    |--------------------------------------------------------------------------
    |
    | This option controls the default session "driver" that will be used by the
    | application when an event needs to be fired. You may set this to
    | any of the connections defined in the "connections" array below.
    |
    | Supported: "file", "cookie", "database", "apc",
    |            "memcached", "redis", "dynamodb", "array"
    |
    */

    'driver' => env('SESSION_DRIVER', 'cookie'),

    /*
    |--------------------------------------------------------------------------
    | Session Lifetime
    |--------------------------------------------------------------------------
    |
    | Here you may define all of the session lengths in minutes. These are used to
    | determine the "lifetime" of the session. You may change these values to
    | suit your application.
    |
    */

    'lifetime' => env('SESSION_LIFETIME', 120),

    /*
    |--------------------------------------------------------------------------
    | Session Encryption
    |--------------------------------------------------------------------------
    |
    | This option controls whether or not session state will be encrypted when it
    | is stored in the browser. This feature is often used to protect session
    | data from being read by unauthorized users.
    |
    */

    'encrypt' => false,

    /*
    |--------------------------------------------------------------------------
    | Session File Location
    |--------------------------------------------------------------------------
    |
    | This option controls the location where the session files are stored. If this
    | option is null, the default location will be used based on your OS.
    |
    */

    'files' => storage_path('framework/sessions'),

    /*
    |--------------------------------------------------------------------------
    | Session Database Connection
    |--------------------------------------------------------------------------
    |
    | When using the "database" session driver, you may specify the connection to
    | use while the "file" session driver for use with a PDO connection. This
    | option should be set to your database connection that is used by your
    | application.
    |
    */

    'connection' => null,

    /*
    |--------------------------------------------------------------------------
    | Session Table
    |--------------------------------------------------------------------------
    |
    | When using the "database" session driver, you may specify the table used to
    | store the session. If this option is null, a table will be created based on
    | the default value of your database connection.
    |
    */

    'table' => 'sessions',

    /*
    |--------------------------------------------------------------------------
    | Session Cache Store
    |--------------------------------------------------------------------------
    |
    | This option controls the cache store that will be used to store session data
    | for the application. This option is used when you are using the "apc",
    | "memcached", or "redis" session drivers.
    |
    */

    'store' => null,

    /*
    |--------------------------------------------------------------------------
    | Session Sweeping
    |--------------------------------------------------------------------------
    |
    | This option controls the frequency of the automatic session sweeping that
    | will be used to clear out old sessions from storage. This option is used
    | when you are using the "apc", "memcached", or "redis" session drivers.
    |
    */

    'lottery' => [2, 100],

    /*
    |--------------------------------------------------------------------------
    | HTTP Access
    |--------------------------------------------------------------------------
    |
    | This option controls the HTTP access that will be used to access the session
    | data. You may use this option to customize the HTTP access that is used to
    | access the session data.
    |
    */

    'http_only' => true,

    /*
    |--------------------------------------------------------------------------
    | HTTPS Access
    |--------------------------------------------------------------------------
    |
    | This option controls the HTTPS access that will be used to access the session
    | data. You may use this option to customize the HTTPS access that is used to
    | access the session data.
    |
    */

    'secure' => false,

    /*
    |--------------------------------------------------------------------------
    | Same-Site Cookies
    |--------------------------------------------------------------------------
    |
    | This option controls the Same-Site Cookies that will be used to access the
    | session data. You may use this option to customize the Same-Site Cookies
    | that is used to access the session data.
    |
    */

    'same_site' => null,

]; 