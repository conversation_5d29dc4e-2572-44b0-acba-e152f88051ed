#!/bin/bash

# Sonali Bank Website Deployment Script
# Usage: ./deploy.sh

set -e

echo "🚀 Starting deployment for Sonali Bank website..."

# Configuration
APP_DIR="/var/www/sonali"
BACKUP_DIR="/var/backups/sonali"
NGINX_CONF="/etc/nginx/sites-available/sonalibd.org"
NGINX_ENABLED="/etc/nginx/sites-enabled/sonalibd.org"
PHP_VERSION="8.2"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as root
if [[ $EUID -eq 0 ]]; then
   print_error "This script should not be run as root for security reasons"
   exit 1
fi

# Create backup
print_status "Creating backup..."
sudo mkdir -p $BACKUP_DIR
sudo cp -r $APP_DIR $BACKUP_DIR/backup-$(date +%Y%m%d-%H%M%S) 2>/dev/null || true

# Update system packages
print_status "Updating system packages..."
sudo apt update

# Install required packages
print_status "Installing required packages..."
sudo apt install -y nginx mysql-server redis-server php$PHP_VERSION-fpm php$PHP_VERSION-mysql php$PHP_VERSION-redis php$PHP_VERSION-xml php$PHP_VERSION-curl php$PHP_VERSION-mbstring php$PHP_VERSION-zip php$PHP_VERSION-gd php$PHP_VERSION-intl php$PHP_VERSION-bcmath composer nodejs npm certbot python3-certbot-nginx

# Set up environment
print_status "Setting up production environment..."
cd $APP_DIR
cp .env.production .env

# Install PHP dependencies
print_status "Installing PHP dependencies..."
composer install --no-dev --optimize-autoloader

# Install Node.js dependencies and build assets
print_status "Building frontend assets..."
npm install
npm run build

# Set up database
print_status "Setting up database..."
php artisan key:generate --force
php artisan config:cache
php artisan route:cache
php artisan view:cache
php artisan migrate --force
php artisan storage:link

# Set proper permissions
print_status "Setting file permissions..."
sudo chown -R www-data:www-data $APP_DIR
sudo chmod -R 755 $APP_DIR
sudo chmod -R 775 $APP_DIR/storage
sudo chmod -R 775 $APP_DIR/bootstrap/cache

# Configure Nginx
print_status "Configuring Nginx..."
sudo cp nginx-sonalibd.conf $NGINX_CONF
sudo ln -sf $NGINX_CONF $NGINX_ENABLED
sudo nginx -t

# Configure PHP-FPM
print_status "Configuring PHP-FPM..."
sudo systemctl enable php$PHP_VERSION-fpm
sudo systemctl start php$PHP_VERSION-fpm

# Start services
print_status "Starting services..."
sudo systemctl enable nginx
sudo systemctl enable mysql
sudo systemctl enable redis-server
sudo systemctl restart nginx
sudo systemctl restart mysql
sudo systemctl restart redis-server

# Set up SSL certificate
print_status "Setting up SSL certificate..."
print_warning "Please run the following command manually after DNS is configured:"
print_warning "sudo certbot --nginx -d sonalibd.org -d www.sonalibd.org"

# Set up cron jobs
print_status "Setting up cron jobs..."
(crontab -l 2>/dev/null; echo "* * * * * cd $APP_DIR && php artisan schedule:run >> /dev/null 2>&1") | crontab -

print_status "✅ Deployment completed successfully!"
print_warning "⚠️  Don't forget to:"
print_warning "1. Configure your domain DNS to point to this server"
print_warning "2. Update database credentials in .env file"
print_warning "3. Update email credentials in .env file"
print_warning "4. Run SSL certificate setup: sudo certbot --nginx -d sonalibd.org -d www.sonalibd.org"
print_warning "5. Create database user and grant permissions"
print_warning "6. Test the website functionality"

echo ""
print_status "🌐 Your website should be accessible at: https://www.sonalibd.org"
