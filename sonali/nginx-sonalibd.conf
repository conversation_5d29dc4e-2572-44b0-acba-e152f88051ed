server {
    listen 80;
    server_name sonalibd.org www.sonalibd.org;
    return 301 https://www.sonalibd.org$request_uri;
}

server {
    listen 443 ssl http2;
    server_name sonalibd.org;
    return 301 https://www.sonalibd.org$request_uri;
    
    ssl_certificate /etc/ssl/certs/sonalibd.org.crt;
    ssl_certificate_key /etc/ssl/private/sonalibd.org.key;
}

server {
    listen 443 ssl http2;
    server_name www.sonalibd.org;
    root /var/www/sonali/public;
    index index.php index.html index.htm;

    # SSL Configuration
    ssl_certificate /etc/ssl/certs/sonalibd.org.crt;
    ssl_certificate_key /etc/ssl/private/sonalibd.org.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-SHA384;
    ssl_prefer_server_ciphers on;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # Security Headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

    # Gzip Compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied expired no-cache no-store private auth;
    gzip_types text/plain text/css text/xml text/javascript application/x-javascript application/xml+rss application/javascript;

    # File Upload Size
    client_max_body_size 100M;

    # Logging
    access_log /var/log/nginx/sonalibd.org.access.log;
    error_log /var/log/nginx/sonalibd.org.error.log;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location = /favicon.ico { access_log off; log_not_found off; }
    location = /robots.txt  { access_log off; log_not_found off; }

    error_page 404 /index.php;

    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.3-fpm.sock;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
        fastcgi_hide_header X-Powered-By;
    }

    location ~ /\.(?!well-known).* {
        deny all;
    }

    # Cache static assets
    location ~* \.(jpg|jpeg|png|gif|ico|css|js|pdf|txt)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # Deny access to sensitive files
    location ~ /\.(env|git|svn) {
        deny all;
        return 404;
    }

    location ~ /(storage|bootstrap/cache) {
        deny all;
        return 404;
    }
}
