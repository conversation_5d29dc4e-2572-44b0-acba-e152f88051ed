-- Sonali Bank Database Setup Script
-- Run this script as MySQL root user

-- Create database
CREATE DATABASE IF NOT EXISTS sonali_production CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Create user
CREATE USER IF NOT EXISTS 'sonali_prod_user'@'localhost' IDENTIFIED BY 'CHANGE_THIS_STRONG_PASSWORD';

-- <PERSON> privileges
GRANT ALL PRIVILEGES ON sonali_production.* TO 'sonali_prod_user'@'localhost';

-- Flush privileges
FLUSH PRIVILEGES;

-- Show databases to confirm
SHOW DATABASES;

-- Show user to confirm
SELECT User, Host FROM mysql.user WHERE User = 'sonali_prod_user';
