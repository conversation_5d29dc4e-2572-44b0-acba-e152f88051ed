#!/bin/bash

# Production Optimization Script for Sonali Bank
# This script optimizes the Laravel application for production

set -e

echo "🔧 Optimizing Laravel application for production..."

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Navigate to application directory
cd /var/www/sonali

# Clear all caches first
print_status "Clearing existing caches..."
php artisan cache:clear
php artisan config:clear
php artisan route:clear
php artisan view:clear

# Generate application key if not exists
print_status "Ensuring application key is set..."
php artisan key:generate --force

# Cache configuration
print_status "Caching configuration..."
php artisan config:cache

# Cache routes
print_status "Caching routes..."
php artisan route:cache

# Cache views
print_status "Caching views..."
php artisan view:cache

# Cache events
print_status "Caching events..."
php artisan event:cache

# Optimize autoloader
print_status "Optimizing Composer autoloader..."
composer dump-autoload --optimize --no-dev

# Create symbolic link for storage
print_status "Creating storage symbolic link..."
php artisan storage:link

# Set proper permissions
print_status "Setting proper file permissions..."
sudo chown -R www-data:www-data /var/www/sonali
sudo chmod -R 755 /var/www/sonali
sudo chmod -R 775 /var/www/sonali/storage
sudo chmod -R 775 /var/www/sonali/bootstrap/cache

# Optimize images and assets (if needed)
print_status "Building and optimizing frontend assets..."
npm run build

# Queue restart (if using queue workers)
print_status "Restarting queue workers..."
php artisan queue:restart

# Clear and warm up OPcache
print_status "Optimizing PHP OPcache..."
sudo systemctl reload php8.2-fpm

print_status "✅ Production optimization completed!"
print_warning "Remember to:"
print_warning "1. Monitor application logs: tail -f storage/logs/laravel.log"
print_warning "2. Monitor server resources"
print_warning "3. Set up regular backups"
print_warning "4. Monitor SSL certificate expiration"
