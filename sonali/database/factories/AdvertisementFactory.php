<?php

namespace Database\Factories;

use App\Models\Advertisement;
use Illuminate\Database\Eloquent\Factories\Factory;

class AdvertisementFactory extends Factory
{
    protected $model = Advertisement::class;

    public function definition(): array
    {
        return [
            'title' => $this->faker->catchPhrase,
            'image' => 'ads/sample_' . $this->faker->numberBetween(1, 5) . '.jpg',
            'link_url' => $this->faker->url,
            'active' => $this->faker->boolean(80),
        ];
    }
} 