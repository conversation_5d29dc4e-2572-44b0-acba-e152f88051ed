<?php

namespace Database\Factories;

use App\Models\Member;
use App\Models\Branch;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

class MemberFactory extends Factory
{
    protected $model = Member::class;

    public function definition(): array
    {
        $branch = Branch::inRandomOrder()->first() ?? Branch::factory()->create();
        $memberId = $branch->code . '-' . strtoupper(Str::random(6));
        return [
            'member_id' => $memberId,
            'name' => $this->faker->name('male'),
            'father_or_husband_name' => $this->faker->name('male'),
            'mother_name' => $this->faker->name('female'),
            'present_address' => $this->faker->address,
            'permanent_address' => $this->faker->address,
            'nid_number' => $this->faker->numerify('19##########'),
            'date_of_birth' => $this->faker->date('Y-m-d', '-18 years'),
            'religion' => $this->faker->randomElement(['Islam', 'Hinduism', 'Buddhism', 'Christianity']),
            'phone_number' => $this->faker->numerify('01#########'),
            'blood_group' => $this->faker->randomElement(['A+', 'A-', 'B+', 'B-', 'O+', 'O-', 'AB+', 'AB-']),
            'photo' => null,
            'occupation' => $this->faker->jobTitle,
            'reference_id' => null,
            'branch_id' => $branch->id,
            'created_by' => User::inRandomOrder()->first()?->id ?? User::factory(),
            'kyc_status' => 'pending',
            'kyc_documents_path' => null,
        ];
    }
} 