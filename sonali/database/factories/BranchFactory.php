<?php

namespace Database\Factories;

use App\Models\Branch;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class BranchFactory extends Factory
{
    protected $model = Branch::class;

    public function definition(): array
    {
        return [
            'name' => $this->faker->company . ' Branch',
            'code' => strtoupper($this->faker->unique()->bothify('BR###')),
            'address' => $this->faker->address,
            'manager_id' => User::factory(),
        ];
    }
} 