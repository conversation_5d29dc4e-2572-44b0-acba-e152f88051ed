<?php

namespace Database\Factories;

use App\Models\BranchTransaction;
use App\Models\Branch;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class BranchTransactionFactory extends Factory
{
    protected $model = BranchTransaction::class;

    public function definition(): array
    {
        $branch = Branch::inRandomOrder()->first() ?? Branch::factory()->create();
        $categories = ['loan_disbursement', 'installment_collection', 'operational_expense', 'salary', 'misc_income'];
        return [
            'branch_id' => $branch->id,
            'entry_type' => $this->faker->randomElement(['income', 'expense']),
            'serial_no' => null, // auto-incremented in model
            'date' => $this->faker->date(),
            'description' => $this->faker->sentence,
            'account_no' => $this->faker->bankAccountNumber,
            'category' => $this->faker->randomElement($categories),
            'voucher_no' => $this->faker->bothify('VCH-####'),
            'amount' => $this->faker->randomFloat(2, 100, 10000),
            'entered_by' => User::inRandomOrder()->first()?->id ?? User::factory(),
            'status' => $this->faker->randomElement(['pending', 'approved', 'rejected']),
        ];
    }
} 