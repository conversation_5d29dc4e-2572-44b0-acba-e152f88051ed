<?php

namespace Database\Factories;

use App\Models\Installment;
use App\Models\Loan;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class InstallmentFactory extends Factory
{
    protected $model = Installment::class;

    public function definition(): array
    {
        $loan = Loan::inRandomOrder()->first() ?? Loan::factory()->create();
        $installmentNo = $this->faker->numberBetween(1, $loan->installment_count);
        $date = $this->faker->dateTimeBetween($loan->first_installment_date, $loan->last_installment_date);
        $amount = $loan->installment_amount;
        $status = $this->faker->randomElement(['pending', 'paid', 'overdue']);
        return [
            'loan_id' => $loan->id,
            'installment_no' => $installmentNo,
            'installment_date' => $date,
            'installment_amount' => $amount,
            'advance_paid' => 0,
            'due_amount' => $status === 'paid' ? 0 : $amount,
            'collected_by' => $status === 'paid' ? (User::inRandomOrder()->first()?->id ?? User::factory()) : null,
            'collection_date' => $status === 'paid' ? $date : null,
            'status' => $status,
        ];
    }
} 