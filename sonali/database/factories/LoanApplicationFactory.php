<?php

namespace Database\Factories;

use App\Models\LoanApplication;
use App\Models\Member;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class LoanApplicationFactory extends Factory
{
    protected $model = LoanApplication::class;

    public function definition(): array
    {
        $member = Member::inRandomOrder()->first() ?? Member::factory()->create();
        return [
            'member_id' => $member->id,
            'applied_amount' => $this->faker->numberBetween(10000, 100000),
            'reason' => $this->faker->sentence,
            'loan_cycle_number' => $this->faker->numberBetween(1, 5),
            'recommender_id' => null,
            'status' => 'pending',
            'advance_payment' => $this->faker->numberBetween(0, 5000),
            'reviewed_by' => User::inRandomOrder()->first()?->id,
            'reviewed_at' => null,
            'applied_at' => $this->faker->dateTimeThisYear(),
        ];
    }
} 