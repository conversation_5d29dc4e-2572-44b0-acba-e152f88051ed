<?php

namespace Database\Factories;

use App\Models\SavingAccount;
use App\Models\Member;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class SavingAccountFactory extends Factory
{
    protected $model = SavingAccount::class;

    public function definition(): array
    {
        $member = Member::inRandomOrder()->first() ?? Member::factory()->create();
        $user = User::inRandomOrder()->first()?->id ?? User::factory();
        $type = $this->faker->randomElement(['general', 'dps', 'fdr']);
        $monthly = $type !== 'fdr' ? $this->faker->numberBetween(500, 5000) : null;
        $fdr = $type === 'fdr' ? $this->faker->numberBetween(10000, 100000) : null;
        return [
            'member_id' => $member->id,
            'saving_type' => $type,
            'joint_photo' => null,
            'nominee_name' => $this->faker->name,
            'nominee_relation' => $this->faker->randomElement(['father', 'mother', 'spouse', 'sibling', 'child']),
            'saving_method' => $this->faker->randomElement(['cash', 'bank_transfer', 'mobile_banking']),
            'monthly_amount' => $monthly,
            'fdr_amount' => $fdr,
            'start_date' => $this->faker->date(),
            'created_by' => $user,
        ];
    }
} 