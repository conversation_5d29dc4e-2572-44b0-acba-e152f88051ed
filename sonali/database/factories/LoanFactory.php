<?php

namespace Database\Factories;

use App\Models\Loan;
use App\Models\LoanApplication;
use Illuminate\Database\Eloquent\Factories\Factory;

class LoanFactory extends Factory
{
    protected $model = Loan::class;

    public function definition(): array
    {
        $application = LoanApplication::inRandomOrder()->first() ?? LoanApplication::factory()->create();
        $loanAmount = $application->applied_amount;
        $installmentCount = $this->faker->numberBetween(6, 36);
        $repaymentDuration = $installmentCount;
        $installmentAmount = round($loanAmount / $installmentCount, 2);
        $totalRepayment = $installmentAmount * $installmentCount;
        $firstInstallmentDate = $this->faker->dateTimeBetween('now', '+1 month');
        $lastInstallmentDate = (clone $firstInstallmentDate)->modify(">{$installmentCount} months");
        return [
            'loan_application_id' => $application->id,
            'loan_date' => $this->faker->date(),
            'loan_amount' => $loanAmount,
            'total_repayment_amount' => $totalRepayment,
            'repayment_duration' => $repaymentDuration,
            'repayment_method' => $this->faker->randomElement(['monthly', 'weekly']),
            'installment_count' => $installmentCount,
            'installment_amount' => $installmentAmount,
            'advance_payment' => $application->advance_payment,
            'first_installment_date' => $firstInstallmentDate,
            'last_installment_date' => $lastInstallmentDate,
        ];
    }
} 