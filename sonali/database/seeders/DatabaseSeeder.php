<?php

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // User::factory(10)->create();

        User::factory()->create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
        ]);

        $this->call(BranchSeeder::class);
        $this->call(MemberSeeder::class);
        $this->call(LoanSeeder::class);
        $this->call(InstallmentSeeder::class);
        $this->call(BranchTransactionSeeder::class);
        $this->call(SavingAccountSeeder::class);
        $this->call(AdvertisementSeeder::class);
    }
}
