<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('installments', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('loan_id');
            $table->unsignedInteger('installment_no');
            $table->date('installment_date');
            $table->decimal('installment_amount', 16, 2);
            $table->decimal('advance_paid', 16, 2)->default(0);
            $table->decimal('due_amount', 16, 2)->default(0);
            $table->unsignedBigInteger('collected_by')->nullable();
            $table->date('collection_date')->nullable();
            $table->string('status')->default('pending');
            $table->timestamps();
            $table->foreign('loan_id')->references('id')->on('loans')->cascadeOnDelete();
            $table->foreign('collected_by')->references('id')->on('users')->nullOnDelete();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('installments');
    }
}; 