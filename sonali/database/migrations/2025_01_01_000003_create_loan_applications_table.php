<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('loan_applications', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('member_id');
            $table->decimal('applied_amount', 16, 2);
            $table->string('reason')->nullable();
            $table->unsignedInteger('loan_cycle_number')->default(1);
            $table->unsignedBigInteger('recommender_id')->nullable();
            $table->string('status')->default('pending');
            $table->decimal('advance_payment', 16, 2)->default(0);
            $table->unsignedBigInteger('reviewed_by')->nullable();
            $table->timestamp('reviewed_at')->nullable();
            $table->timestamp('applied_at')->nullable();
            $table->json('documents')->nullable();
            $table->json('audit_trail')->nullable();
            $table->timestamps();
            $table->foreign('member_id')->references('id')->on('members')->cascadeOnDelete();
            $table->foreign('recommender_id')->references('id')->on('members')->nullOnDelete();
            $table->foreign('reviewed_by')->references('id')->on('users')->nullOnDelete();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('loan_applications');
    }
}; 