<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('saving_accounts', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('member_id');
            $table->enum('saving_type', ['general', 'dps', 'fdr']);
            $table->string('joint_photo')->nullable();
            $table->string('nominee_name')->nullable();
            $table->string('nominee_relation')->nullable();
            $table->string('saving_method')->nullable();
            $table->decimal('monthly_amount', 16, 2)->nullable();
            $table->decimal('fdr_amount', 16, 2)->nullable();
            $table->date('start_date')->nullable();
            $table->unsignedBigInteger('created_by');
            $table->timestamps();
            $table->foreign('member_id')->references('id')->on('members')->cascadeOnDelete();
            $table->foreign('created_by')->references('id')->on('users')->cascadeOnDelete();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('saving_accounts');
    }
}; 