<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('loans', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('loan_application_id');
            $table->date('loan_date');
            $table->decimal('loan_amount', 16, 2);
            $table->decimal('total_repayment_amount', 16, 2);
            $table->unsignedInteger('repayment_duration');
            $table->string('repayment_method');
            $table->unsignedInteger('installment_count');
            $table->decimal('installment_amount', 16, 2);
            $table->decimal('advance_payment', 16, 2)->default(0);
            $table->date('first_installment_date');
            $table->date('last_installment_date');
            $table->string('disbursement_status')->nullable();
            $table->timestamps();
            $table->foreign('loan_application_id')->references('id')->on('loan_applications')->cascadeOnDelete();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('loans');
    }
}; 