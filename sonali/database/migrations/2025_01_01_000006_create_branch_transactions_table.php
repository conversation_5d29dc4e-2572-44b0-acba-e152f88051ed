<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('branch_transactions', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('branch_id');
            $table->enum('entry_type', ['income', 'expense']);
            $table->unsignedInteger('serial_no');
            $table->date('date');
            $table->string('description')->nullable();
            $table->string('account_no')->nullable();
            $table->string('category');
            $table->string('voucher_no')->nullable();
            $table->decimal('amount', 16, 2);
            $table->unsignedBigInteger('entered_by');
            $table->string('status')->default('pending');
            $table->timestamps();
            $table->foreign('branch_id')->references('id')->on('branches')->cascadeOnDelete();
            $table->foreign('entered_by')->references('id')->on('users')->cascadeOnDelete();
            $table->index(['branch_id', 'serial_no']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('branch_transactions');
    }
}; 