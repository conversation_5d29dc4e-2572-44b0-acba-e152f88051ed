@extends('layouts.app')

@section('breadcrumbs')
<x-breadcrumbs :links="[
    ['url' => '/member', 'label' => 'Member Portal'],
    ['url' => '/member/dashboard', 'label' => 'Dashboard'],
]" />
@endsection

@section('content')
<div class="max-w-4xl mx-auto p-6 bg-white dark:bg-gray-800 rounded shadow mt-8">
    <h2 class="text-2xl font-bold mb-4">Welcome, {{ $member->name }}</h2>
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <div class="bg-blue-100 dark:bg-blue-900 p-4 rounded">
            <div class="text-sm text-gray-500">Account Status</div>
            <div class="text-xl font-bold">{{ ucfirst($member->status ?? 'Active') }}</div>
        </div>
        <div class="bg-green-100 dark:bg-green-900 p-4 rounded">
            <div class="text-sm text-gray-500">Loan Status</div>
            <div class="text-xl font-bold">{{ $loanStatus }}</div>
        </div>
        <div class="bg-yellow-100 dark:bg-yellow-900 p-4 rounded">
            <div class="text-sm text-gray-500">Savings Balance</div>
            <div class="text-xl font-bold">৳{{ number_format($savingsBalance, 2) }}</div>
        </div>
    </div>
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <div class="bg-white dark:bg-gray-900 p-4 rounded shadow">
            <h3 class="font-semibold mb-2">Recent Notifications</h3>
            <ul class="divide-y divide-gray-200 dark:divide-gray-700">
                @forelse($notifications as $note)
                    <li class="py-2 flex justify-between items-center">
                        <span>{{ $note->data['message'] ?? $note->data['title'] ?? 'Notification' }}</span>
                        <span class="text-xs text-gray-500">{{ $note->created_at->diffForHumans() }}</span>
                    </li>
                @empty
                    <li class="py-2 text-gray-500">No notifications.</li>
                @endforelse
            </ul>
        </div>
        <div class="bg-white dark:bg-gray-900 p-4 rounded shadow flex flex-col gap-3">
            <a href="{{ route('member.profile') }}" class="bg-blue-600 text-white px-4 py-2 rounded text-center">Update Profile</a>
            <a href="{{ route('member.installments') }}" class="bg-green-600 text-white px-4 py-2 rounded text-center">View Installment History</a>
            <a href="{{ route('member.savings') }}" class="bg-yellow-600 text-white px-4 py-2 rounded text-center">Savings Account</a>
            <a href="{{ route('member.documents') }}" class="bg-gray-700 text-white px-4 py-2 rounded text-center">Download Documents</a>
            <a href="{{ route('member.notifications') }}" class="bg-indigo-600 text-white px-4 py-2 rounded text-center">Notification Preferences</a>
        </div>
    </div>
</div>
@endsection 