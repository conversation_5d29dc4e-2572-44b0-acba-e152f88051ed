<div x-data="{ error: null }" @error.window="error = $event.detail" class="relative">
    <template x-if="error">
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
            <strong class="font-bold">Error:</strong>
            <span class="block sm:inline" x-text="error"></span>
            <span @click="error = null" class="absolute top-0 bottom-0 right-0 px-4 py-3 cursor-pointer">&times;</span>
        </div>
    </template>
    <div x-show="!error">
        {{ $slot }}
    </div>
</div> 