@props(['show' => false, 'title' => ''])
<div x-data="{ open: @js($show) }" x-show="open" x-transition:enter="transition ease-out duration-200" x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100" x-transition:leave="transition ease-in duration-150" x-transition:leave-start="opacity-100" x-transition:leave-end="opacity-0" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50" role="dialog" aria-modal="true" aria-labelledby="modal-title">
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg w-full max-w-lg mx-4">
        <div class="flex justify-between items-center border-b px-4 py-2">
            <h3 class="text-lg font-semibold" id="modal-title">{{ $title }}</h3>
            <button @click="open = false" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-200" aria-label="Close">&times;</button>
        </div>
        <div class="p-4">
            {{ $slot }}
        </div>
    </div>
</div>
