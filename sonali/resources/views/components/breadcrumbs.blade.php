@props(['links' => []])
<nav class="flex text-sm text-gray-500 dark:text-gray-300" aria-label="Breadcrumb" itemscope itemtype="https://schema.org/BreadcrumbList">
    <ol class="inline-flex items-center space-x-1">
        @foreach($links as $i => $link)
            <li itemprop="itemListElement" itemscope itemtype="https://schema.org/ListItem">
                <a href="{{ $link['url'] }}" class="hover:underline" itemprop="item">
                    <span itemprop="name">{{ $link['label'] }}</span>
                </a>
                <meta itemprop="position" content="{{ $i + 1 }}" />
            </li>
            @if(!$loop->last)
                <span>/</span>
            @endif
        @endforeach
    </ol>
</nav> 