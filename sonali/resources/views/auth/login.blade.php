@extends('layouts.app')

@section('content')
<div class="min-h-screen flex items-center justify-center bg-gray-100 dark:bg-gray-900">
    <div class="w-full max-w-md p-8 space-y-6 bg-white dark:bg-gray-800 rounded shadow">
        <h2 class="text-2xl font-bold text-center mb-2">Login</h2>
        @if(session('status'))
            <div class="mb-4 text-green-600">{{ session('status') }}</div>
        @endif
        <x-error-boundary>
        <form method="POST" action="{{ route('login') }}" class="space-y-4" x-data="{ loading: false }" @submit.prevent="loading = true; $el.submit()">
            @csrf
            <div>
                <label for="email" class="block text-sm font-medium">Email</label>
                <input id="email" type="email" name="email" value="{{ old('email') }}" required autofocus class="w-full border rounded px-3 py-2 mt-1 @error('email') border-red-500 @enderror">
                @error('email')<span class="text-red-500 text-xs">{{ $message }}</span>@enderror
            </div>
            <div>
                <label for="password" class="block text-sm font-medium">Password</label>
                <input id="password" type="password" name="password" required class="w-full border rounded px-3 py-2 mt-1 @error('password') border-red-500 @enderror">
                @error('password')<span class="text-red-500 text-xs">{{ $message }}</span>@enderror
            </div>
            <div>
                <label for="role" class="block text-sm font-medium">Role</label>
                <select id="role" name="role" class="w-full border rounded px-3 py-2 mt-1">
                    <option value="">Select Role</option>
                    <option value="admin">Admin</option>
                    <option value="manager">Manager</option>
                    <option value="field_officer">Field Officer</option>
                    <option value="member">Member</option>
                </select>
            </div>
            <div class="flex items-center justify-between">
                <label class="flex items-center">
                    <input type="checkbox" name="remember" class="mr-2"> Remember me
                </label>
                <a href="{{ route('password.request') }}" class="text-sm text-blue-600 hover:underline">Forgot password?</a>
            </div>
            <button type="submit" class="w-full bg-blue-600 text-white py-2 rounded hover:bg-blue-700 flex items-center justify-center">
                <span x-show="!loading">Login</span>
                <x-skeleton width="1.5rem" height="1.5rem" x-show="loading" />
            </button>
        </form>
        </x-error-boundary>
        @if($ads->count())
        <div class="mt-6">
            <h3 class="text-lg font-semibold mb-2">Sponsored</h3>
            <div class="grid grid-cols-1 gap-4">
                @foreach($ads as $ad)
                    <a href="{{ $ad->link_url }}" target="_blank" class="block border rounded p-2 hover:shadow">
                        <img src="{{ $ad->image_url }}" alt="{{ $ad->title }}" class="w-full h-24 object-cover rounded mb-1">
                        <div class="text-sm text-center">{{ $ad->title }}</div>
                    </a>
                @endforeach
            </div>
        </div>
        @endif
    </div>
</div>
@endsection
