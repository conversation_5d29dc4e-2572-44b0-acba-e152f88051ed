@extends('layouts.app')

@section('content')
<div class="min-h-screen flex items-center justify-center bg-gray-100 dark:bg-gray-900">
    <div class="w-full max-w-md p-8 space-y-6 bg-white dark:bg-gray-800 rounded shadow">
        <h2 class="text-2xl font-bold text-center mb-2">Set New Password</h2>
        <x-error-boundary>
        <form method="POST" action="{{ route('password.update') }}" class="space-y-4" x-data="{ loading: false }" @submit.prevent="loading = true; $el.submit()">
            @csrf
            <input type="hidden" name="token" value="{{ $token }}">
            <div>
                <label for="email" class="block text-sm font-medium">Email</label>
                <input id="email" type="email" name="email" value="{{ old('email') }}" required autofocus class="w-full border rounded px-3 py-2 mt-1 @error('email') border-red-500 @enderror">
                @error('email')<span class="text-red-500 text-xs">{{ $message }}</span>@enderror
            </div>
            <div>
                <label for="password" class="block text-sm font-medium">New Password</label>
                <input id="password" type="password" name="password" required class="w-full border rounded px-3 py-2 mt-1 @error('password') border-red-500 @enderror">
                @error('password')<span class="text-red-500 text-xs">{{ $message }}</span>@enderror
            </div>
            <div>
                <label for="password-confirm" class="block text-sm font-medium">Confirm Password</label>
                <input id="password-confirm" type="password" name="password_confirmation" required class="w-full border rounded px-3 py-2 mt-1">
            </div>
            <button type="submit" class="w-full bg-blue-600 text-white py-2 rounded hover:bg-blue-700 flex items-center justify-center">
                <span x-show="!loading">Reset Password</span>
                <x-skeleton width="1.5rem" height="1.5rem" x-show="loading" />
            </button>
        </form>
        </x-error-boundary>
    </div>
</div>
@endsection 