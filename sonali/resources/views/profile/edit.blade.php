@extends('layouts.app')

@section('content')
<div class="max-w-xl mx-auto p-6 bg-white dark:bg-gray-800 rounded shadow mt-8">
    <h2 class="text-2xl font-bold mb-4">Edit Profile</h2>
    <x-error-boundary>
    <form method="POST" action="{{ route('profile.update') }}" class="space-y-4" x-data="{ loading: false }" @submit.prevent="loading = true; $el.submit()">
        @csrf
        @method('PUT')
        <div>
            <label for="name" class="block text-sm font-medium">Name</label>
            <input id="name" type="text" name="name" value="{{ old('name', $user->name) }}" required class="w-full border rounded px-3 py-2 mt-1 @error('name') border-red-500 @enderror">
            @error('name')<span class="text-red-500 text-xs">{{ $message }}</span>@enderror
        </div>
        <div>
            <label for="email" class="block text-sm font-medium">Email</label>
            <input id="email" type="email" name="email" value="{{ old('email', $user->email) }}" required class="w-full border rounded px-3 py-2 mt-1 @error('email') border-red-500 @enderror">
            @error('email')<span class="text-red-500 text-xs">{{ $message }}</span>@enderror
        </div>
        <div>
            <label for="phone" class="block text-sm font-medium">Phone</label>
            <input id="phone" type="text" name="phone" value="{{ old('phone', $user->phone) }}" class="w-full border rounded px-3 py-2 mt-1 @error('phone') border-red-500 @enderror">
            @error('phone')<span class="text-red-500 text-xs">{{ $message }}</span>@enderror
        </div>
        <div>
            <label for="password" class="block text-sm font-medium">New Password</label>
            <input id="password" type="password" name="password" class="w-full border rounded px-3 py-2 mt-1 @error('password') border-red-500 @enderror">
            @error('password')<span class="text-red-500 text-xs">{{ $message }}</span>@enderror
        </div>
        <div>
            <label for="language" class="block text-sm font-medium">Language</label>
            <select id="language" name="language" class="w-full border rounded px-3 py-2 mt-1">
                <option value="en" @if($user->language === 'en') selected @endif>English</option>
                <option value="bn" @if($user->language === 'bn') selected @endif>বাংলা</option>
            </select>
        </div>
        <button type="submit" class="w-full bg-blue-600 text-white py-2 rounded hover:bg-blue-700 flex items-center justify-center">
            <span x-show="!loading">Update Profile</span>
            <x-skeleton width="1.5rem" height="1.5rem" x-show="loading" />
        </button>
    </form>
    </x-error-boundary>
</div>
@endsection
