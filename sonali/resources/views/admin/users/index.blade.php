@extends('layouts.app')

@section('breadcrumbs')
<x-breadcrumbs :links="[
    ['url' => '/admin', 'label' => 'Admin'],
    ['url' => '/admin/users', 'label' => 'User Management'],
]" />
@endsection

@section('content')
<div class="max-w-7xl mx-auto p-6 bg-white dark:bg-gray-800 rounded shadow mt-8">
    <h2 class="text-2xl font-bold mb-4">User Management</h2>
    <form method="GET" action="" class="mb-4 flex flex-wrap gap-2 items-center">
        <input type="text" name="q" value="{{ request('q') }}" placeholder="Search users..." class="border rounded px-3 py-2" />
        <select name="role" class="border rounded px-3 py-2">
            <option value="">All Roles</option>
            @foreach($roles as $role)
                <option value="{{ $role }}" @selected(request('role')==$role)> {{ ucfirst($role) }} </option>
            @endforeach
        </select>
        <button type="submit" class="bg-blue-600 text-white px-4 py-2 rounded">Filter</button>
    </form>
    <form method="POST" action="{{ route('admin.users.bulk') }}">
        @csrf
        <div class="overflow-x-auto">
            <table class="min-w-full table-auto text-sm">
                <thead>
                    <tr class="bg-gray-100 dark:bg-gray-700">
                        <th class="p-2"><input type="checkbox" @click="toggleAll($event)"></th>
                        <th class="p-2">Name</th>
                        <th class="p-2">Email</th>
                        <th class="p-2">Role</th>
                        <th class="p-2">Branch</th>
                        <th class="p-2">Status</th>
                        <th class="p-2">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($users as $user)
                        <tr class="border-b">
                            <td class="p-2"><input type="checkbox" name="ids[]" value="{{ $user->id }}"></td>
                            <td class="p-2">{{ $user->name }}</td>
                            <td class="p-2">{{ $user->email }}</td>
                            <td class="p-2">{{ ucfirst($user->role) }}</td>
                            <td class="p-2">{{ $user->branch?->name ?? '-' }}</td>
                            <td class="p-2">{{ $user->status ?? 'Active' }}</td>
                            <td class="p-2 flex gap-2">
                                <a href="{{ route('admin.users.show', $user) }}" class="text-blue-600 hover:underline">View</a>
                                <a href="{{ route('admin.users.edit', $user) }}" class="text-yellow-600 hover:underline">Edit</a>
                                <form method="POST" action="{{ route('admin.users.destroy', $user) }}" onsubmit="return confirm('Delete user?')">
                                    @csrf @method('DELETE')
                                    <button class="text-red-600 hover:underline">Delete</button>
                                </form>
                                <a href="{{ route('admin.users.assignRole', $user) }}" class="text-indigo-600 hover:underline">Assign Role</a>
                            </td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
        <div class="mt-4 flex gap-2">
            <select name="bulk_action" class="border rounded px-3 py-2">
                <option value="">Bulk Actions</option>
                <option value="delete">Delete</option>
                <option value="activate">Activate</option>
                <option value="deactivate">Deactivate</option>
                <option value="assign_role">Assign Role</option>
            </select>
            <button type="submit" class="bg-gray-700 text-white px-4 py-2 rounded">Apply</button>
        </div>
    </form>
    <div class="mt-6">{{ $users->links() }}</div>
</div>
<script>
function toggleAll(e) {
    const checkboxes = document.querySelectorAll('input[type=checkbox][name="ids[]"]');
    for (const cb of checkboxes) cb.checked = e.target.checked;
}
</script>
@endsection 