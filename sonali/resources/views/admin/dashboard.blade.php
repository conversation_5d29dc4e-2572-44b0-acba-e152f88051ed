@extends('layouts.app')

@section('breadcrumbs')
<x-breadcrumbs :links="[
    ['url' => '/admin', 'label' => 'Admin'],
    ['url' => '/admin/dashboard', 'label' => 'Dashboard'],
]" />
@endsection

@section('content')
<div class="max-w-7xl mx-auto p-6 bg-white dark:bg-gray-800 rounded shadow mt-8">
    <h2 class="text-2xl font-bold mb-4">Admin Analytics Dashboard</h2>
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div class="bg-blue-100 dark:bg-blue-900 p-4 rounded">
            <div class="text-sm text-gray-500">Total Users</div>
            <div class="text-2xl font-bold">{{ $stats['users'] }}</div>
        </div>
        <div class="bg-green-100 dark:bg-green-900 p-4 rounded">
            <div class="text-sm text-gray-500">Active Branches</div>
            <div class="text-2xl font-bold">{{ $stats['branches'] }}</div>
        </div>
        <div class="bg-yellow-100 dark:bg-yellow-900 p-4 rounded">
            <div class="text-sm text-gray-500">Total Loans</div>
            <div class="text-2xl font-bold">{{ $stats['loans'] }}</div>
        </div>
        <div class="bg-red-100 dark:bg-red-900 p-4 rounded">
            <div class="text-sm text-gray-500">System Health</div>
            <div class="text-2xl font-bold">{{ $stats['system_health'] }}</div>
        </div>
    </div>
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <div class="bg-white dark:bg-gray-900 p-4 rounded shadow">
            <h3 class="font-semibold mb-2">Multi-Branch Performance</h3>
            <canvas id="branchPerformanceChart" height="180"></canvas>
        </div>
        <div class="bg-white dark:bg-gray-900 p-4 rounded shadow">
            <h3 class="font-semibold mb-2">User Management</h3>
            <ul class="divide-y divide-gray-200 dark:divide-gray-700">
                <li class="py-2 flex justify-between items-center">
                    <span>Admins</span>
                    <span class="font-bold">{{ $userSummary['admins'] }}</span>
                </li>
                <li class="py-2 flex justify-between items-center">
                    <span>Managers</span>
                    <span class="font-bold">{{ $userSummary['managers'] }}</span>
                </li>
                <li class="py-2 flex justify-between items-center">
                    <span>Field Officers</span>
                    <span class="font-bold">{{ $userSummary['officers'] }}</span>
                </li>
                <li class="py-2 flex justify-between items-center">
                    <span>Members</span>
                    <span class="font-bold">{{ $userSummary['members'] }}</span>
                </li>
            </ul>
            <a href="{{ route('admin.users.index') }}" class="mt-3 inline-block text-blue-600 hover:underline">Manage Users</a>
        </div>
    </div>
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <div class="bg-white dark:bg-gray-900 p-4 rounded shadow">
            <h3 class="font-semibold mb-2">Business Intelligence Reports</h3>
            <canvas id="biReportChart" height="180"></canvas>
        </div>
        <div class="bg-white dark:bg-gray-900 p-4 rounded shadow">
            <h3 class="font-semibold mb-2">KPI Tracking</h3>
            <canvas id="kpiChart" height="180"></canvas>
        </div>
    </div>
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <div class="bg-white dark:bg-gray-900 p-4 rounded shadow">
            <h3 class="font-semibold mb-2">Predictive Analytics</h3>
            <div class="text-gray-500 text-sm">(Coming soon: AI-driven forecasts for loan defaults, growth, and more.)</div>
        </div>
        <div class="bg-white dark:bg-gray-900 p-4 rounded shadow">
            <h3 class="font-semibold mb-2">Real-Time System Status</h3>
            <ul class="text-xs text-gray-500">
                <li>API: <span class="font-semibold {{ $stats['api_status']==='Online' ? 'text-green-600' : 'text-red-600' }}">{{ $stats['api_status'] }}</span></li>
                <li>Database: <span class="font-semibold {{ $stats['db_status']==='Online' ? 'text-green-600' : 'text-red-600' }}">{{ $stats['db_status'] }}</span></li>
                <li>Queue: <span class="font-semibold {{ $stats['queue_status']==='Online' ? 'text-green-600' : 'text-red-600' }}">{{ $stats['queue_status'] }}</span></li>
                <li>Cache: <span class="font-semibold {{ $stats['cache_status']==='Online' ? 'text-green-600' : 'text-red-600' }}">{{ $stats['cache_status'] }}</span></li>
            </ul>
        </div>
    </div>
</div>
@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    new Chart(document.getElementById('branchPerformanceChart').getContext('2d'), {
        type: 'bar',
        data: {
            labels: @json(array_keys($branchPerformance)),
            datasets: [{
                label: 'Performance',
                data: @json(array_values($branchPerformance)),
                backgroundColor: '#2563eb',
            }]
        },
        options: {responsive: true, plugins: {legend: {display: false}}}
    });
    new Chart(document.getElementById('biReportChart').getContext('2d'), {
        type: 'line',
        data: {
            labels: @json($biReportLabels),
            datasets: [{
                label: 'BI Metric',
                data: @json($biReportData),
                borderColor: '#34d399',
                backgroundColor: 'rgba(52,211,153,0.1)',
                fill: true,
            }]
        },
        options: {responsive: true, plugins: {legend: {display: false}}}
    });
    new Chart(document.getElementById('kpiChart').getContext('2d'), {
        type: 'radar',
        data: {
            labels: @json($kpiLabels),
            datasets: [{
                label: 'KPI',
                data: @json($kpiData),
                backgroundColor: 'rgba(59,130,246,0.2)',
                borderColor: '#3b82f6',
            }]
        },
        options: {responsive: true, plugins: {legend: {position: 'bottom'}}}
    });
</script>
@endpush
@endsection 