@extends('layouts.app')

@section('breadcrumbs')
<x-breadcrumbs :links="[
    ['url' => '/admin', 'label' => 'Admin'],
    ['url' => '/admin/settings', 'label' => 'System Settings'],
]" />
@endsection

@section('content')
<div class="max-w-5xl mx-auto p-6 bg-white dark:bg-gray-800 rounded shadow mt-8">
    <h2 class="text-2xl font-bold mb-4">System Configuration</h2>
    <div x-data="{ tab: 'general' }">
        <div class="flex gap-4 mb-6 border-b pb-2">
            <button @click="tab='general'" :class="tab==='general' ? 'font-bold text-blue-600' : ''">General</button>
            <button @click="tab='backup'" :class="tab==='backup' ? 'font-bold text-blue-600' : ''">Backup & Restore</button>
            <button @click="tab='maintenance'" :class="tab==='maintenance' ? 'font-bold text-blue-600' : ''">Maintenance</button>
            <button @click="tab='audit'" :class="tab==='audit' ? 'font-bold text-blue-600' : ''">Audit Logs</button>
            <button @click="tab='notifications'" :class="tab==='notifications' ? 'font-bold text-blue-600' : ''">Notifications</button>
            <button @click="tab='import'" :class="tab==='import' ? 'font-bold text-blue-600' : ''">Import/Export</button>
            <button @click="tab='health'" :class="tab==='health' ? 'font-bold text-blue-600' : ''">System Health</button>
        </div>
        <div x-show="tab==='general'">
            <form method="POST" action="{{ route('admin.settings.update') }}" class="space-y-4">
                @csrf
                <div>
                    <label class="block text-sm font-medium mb-1">System Name</label>
                    <input type="text" name="system_name" value="{{ $settings['system_name'] ?? '' }}" class="w-full border rounded px-3 py-2" />
                </div>
                <div>
                    <label class="block text-sm font-medium mb-1">Default Language</label>
                    <select name="default_language" class="w-full border rounded px-3 py-2">
                        <option value="en" @selected(($settings['default_language'] ?? '')==='en')>English</option>
                        <option value="bn" @selected(($settings['default_language'] ?? '')==='bn')>Bangla</option>
                    </select>
                </div>
                <div class="flex justify-end">
                    <button type="submit" class="bg-blue-600 text-white px-6 py-2 rounded">Save Settings</button>
                </div>
            </form>
        </div>
        <div x-show="tab==='backup'">
            <h3 class="font-semibold mb-2">Backup & Restore</h3>
            <form method="POST" action="{{ route('admin.settings.backup') }}" class="mb-2">
                @csrf
                <button type="submit" class="bg-green-600 text-white px-4 py-2 rounded">Create Backup</button>
            </form>
            <form method="POST" action="{{ route('admin.settings.restore') }}" enctype="multipart/form-data">
                @csrf
                <input type="file" name="backup_file" class="border rounded px-3 py-2 mb-2" />
                <button type="submit" class="bg-blue-600 text-white px-4 py-2 rounded">Restore Backup</button>
            </form>
        </div>
        <div x-show="tab==='maintenance'">
            <h3 class="font-semibold mb-2">System Maintenance</h3>
            <form method="POST" action="{{ route('admin.settings.maintenance') }}">
                @csrf
                <label class="inline-flex items-center">
                    <input type="checkbox" name="maintenance_mode" value="1" @checked($settings['maintenance_mode'] ?? false) class="mr-2" />
                    Enable Maintenance Mode
                </label>
                <button type="submit" class="ml-4 bg-yellow-600 text-white px-4 py-2 rounded">Update</button>
            </form>
        </div>
        <div x-show="tab==='audit'">
            <h3 class="font-semibold mb-2">Audit Log Management</h3>
            <a href="{{ route('admin.settings.audit.download') }}" class="bg-gray-700 text-white px-4 py-2 rounded">Download Audit Logs</a>
            <a href="{{ route('admin.settings.audit.clear') }}" class="ml-2 bg-red-600 text-white px-4 py-2 rounded" onclick="return confirm('Clear all audit logs?')">Clear Logs</a>
        </div>
        <div x-show="tab==='notifications'">
            <h3 class="font-semibold mb-2">System Notification Management</h3>
            <form method="POST" action="{{ route('admin.settings.notifications') }}" class="space-y-2">
                @csrf
                <label class="block">Email Notifications
                    <input type="checkbox" name="email_notifications" value="1" @checked($settings['email_notifications'] ?? false) class="ml-2" />
                </label>
                <label class="block">SMS Notifications
                    <input type="checkbox" name="sms_notifications" value="1" @checked($settings['sms_notifications'] ?? false) class="ml-2" />
                </label>
                <div class="flex justify-end">
                    <button type="submit" class="bg-blue-600 text-white px-6 py-2 rounded">Update Notifications</button>
                </div>
            </form>
        </div>
        <div x-show="tab==='import'">
            <h3 class="font-semibold mb-2">Data Import/Export</h3>
            <form method="POST" action="{{ route('admin.settings.import') }}" enctype="multipart/form-data" class="mb-2">
                @csrf
                <input type="file" name="import_file" class="border rounded px-3 py-2 mb-2" />
                <button type="submit" class="bg-blue-600 text-white px-4 py-2 rounded">Import Data</button>
            </form>
            <a href="{{ route('admin.settings.export') }}" class="bg-green-600 text-white px-4 py-2 rounded">Export Data</a>
        </div>
        <div x-show="tab==='health'">
            <h3 class="font-semibold mb-2">System Health Monitoring</h3>
            <ul class="text-sm text-gray-600 dark:text-gray-300">
                <li>API: <span class="font-semibold {{ $health['api']==='Online' ? 'text-green-600' : 'text-red-600' }}">{{ $health['api'] }}</span></li>
                <li>Database: <span class="font-semibold {{ $health['db']==='Online' ? 'text-green-600' : 'text-red-600' }}">{{ $health['db'] }}</span></li>
                <li>Queue: <span class="font-semibold {{ $health['queue']==='Online' ? 'text-green-600' : 'text-red-600' }}">{{ $health['queue'] }}</span></li>
                <li>Cache: <span class="font-semibold {{ $health['cache']==='Online' ? 'text-green-600' : 'text-red-600' }}">{{ $health['cache'] }}</span></li>
            </ul>
        </div>
    </div>
</div>
@endsection 