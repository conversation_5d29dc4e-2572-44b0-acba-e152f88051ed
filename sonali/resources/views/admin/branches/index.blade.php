@extends('layouts.app')

@section('breadcrumbs')
<x-breadcrumbs :links="[
    ['url' => '/admin', 'label' => 'Admin'],
    ['url' => '/admin/branches', 'label' => 'Branch Management'],
]" />
@endsection

@section('content')
<div class="max-w-5xl mx-auto p-6 bg-white dark:bg-gray-800 rounded shadow mt-8">
    <h2 class="text-2xl font-bold mb-4">Branch Management</h2>
    <form method="GET" action="" class="mb-4 flex flex-wrap gap-2 items-center">
        <input type="text" name="q" value="{{ request('q') }}" placeholder="Search branches..." class="border rounded px-3 py-2" />
        <button type="submit" class="bg-blue-600 text-white px-4 py-2 rounded">Search</button>
        <a href="{{ route('admin.branches.create') }}" class="ml-auto bg-green-600 text-white px-4 py-2 rounded">Add Branch</a>
    </form>
    <div class="overflow-x-auto">
        <table class="min-w-full table-auto text-sm">
            <thead>
                <tr class="bg-gray-100 dark:bg-gray-700">
                    <th class="p-2">Name</th>
                    <th class="p-2">Code</th>
                    <th class="p-2">Address</th>
                    <th class="p-2">Manager</th>
                    <th class="p-2">Users</th>
                    <th class="p-2">Actions</th>
                </tr>
            </thead>
            <tbody>
                @foreach($branches as $branch)
                    <tr class="border-b">
                        <td class="p-2">{{ $branch->name }}</td>
                        <td class="p-2">{{ $branch->code }}</td>
                        <td class="p-2">{{ $branch->address }}</td>
                        <td class="p-2">{{ $branch->manager?->name ?? '-' }}</td>
                        <td class="p-2"><a href="{{ route('admin.branches.users', $branch) }}" class="text-blue-600 hover:underline">View Users</a></td>
                        <td class="p-2 flex gap-2">
                            <a href="{{ route('admin.branches.edit', $branch) }}" class="text-yellow-600 hover:underline">Edit</a>
                            <form method="POST" action="{{ route('admin.branches.destroy', $branch) }}" onsubmit="return confirm('Delete branch?')">
                                @csrf @method('DELETE')
                                <button class="text-red-600 hover:underline">Delete</button>
                            </form>
                            <a href="{{ route('admin.branches.assignManager', $branch) }}" class="text-indigo-600 hover:underline">Assign Manager</a>
                        </td>
                    </tr>
                @endforeach
            </tbody>
        </table>
    </div>
    <div class="mt-6">{{ $branches->links() }}</div>
</div>
@endsection 