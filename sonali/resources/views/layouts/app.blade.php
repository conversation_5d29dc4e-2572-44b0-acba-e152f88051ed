<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" x-data="{ dark: localStorage.getItem('theme') === 'dark' }" x-bind:class="{ 'dark': dark }" x-init="if(window.matchMedia('(prefers-color-scheme: dark)').matches){dark=true} else {dark=false}">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta name="csrf-token" content="{{ csrf_token() }}">

        <title>{{ config('app.name', 'Sonali') }}</title>

        <!-- Fonts -->
        <link rel="preconnect" href="https://fonts.bunny.net">
        <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

        <!-- Scripts -->
        @vite(['resources/css/app.css', 'resources/js/app.js'])
        <link rel="stylesheet" href="{{ mix('css/app.css') }}">
        <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
    </head>
    <body class="bg-gray-100 dark:bg-gray-900 text-gray-900 dark:text-gray-100 min-h-screen">
        <div class="grid grid-cols-1 md:grid-cols-[240px_1fr] grid-rows-[auto_1fr_auto] min-h-screen">
            <!-- Sidebar -->
            <aside class="bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 md:block hidden row-span-3">
                <div class="p-4 text-xl font-bold">{{ config('app.name', 'Sonali') }}</div>
                <nav class="mt-6 space-y-2">
                    @role('admin')
                        <a href="/admin/dashboard" class="block px-4 py-2 hover:bg-gray-200 dark:hover:bg-gray-700 rounded">Admin Dashboard</a>
                    @endrole
                    @permission('view members')
                        <a href="/members" class="block px-4 py-2 hover:bg-gray-200 dark:hover:bg-gray-700 rounded">Members</a>
                    @endpermission
                    @permission('view loans')
                        <a href="/loans" class="block px-4 py-2 hover:bg-gray-200 dark:hover:bg-gray-700 rounded">Loans</a>
                    @endpermission
                    @permission('view savings')
                        <a href="/savings" class="block px-4 py-2 hover:bg-gray-200 dark:hover:bg-gray-700 rounded">Savings</a>
                    @endpermission
                    <!-- Add more role/permission-based links here -->
                </nav>
            </aside>
            <!-- Mobile Sidebar Toggle -->
            <div class="md:hidden flex items-center justify-between bg-white dark:bg-gray-800 p-4 border-b border-gray-200 dark:border-gray-700">
                <button @click="sidebarOpen = !sidebarOpen" class="focus:outline-none">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"/></svg>
                </button>
                <div class="text-xl font-bold">{{ config('app.name', 'Sonali') }}</div>
                <button @click="dark = !dark; localStorage.setItem('theme', dark ? 'dark' : 'light')" class="ml-2">
                    <svg x-show="!dark" class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m8.66-8.66l-.71.71M4.05 19.07l-.71.71M21 12h-1M4 12H3m16.66-4.66l-.71-.71M4.05 4.93l-.71-.71"/></svg>
                    <svg x-show="dark" class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.293 17.293A8 8 0 016.707 6.707a8.001 8.001 0 1010.586 10.586z"/></svg>
                </button>
            </div>
            <aside x-show="sidebarOpen" @click.away="sidebarOpen = false" class="fixed inset-0 z-40 bg-black bg-opacity-50 flex md:hidden">
                <div class="w-64 bg-white dark:bg-gray-800 p-4 space-y-2">
                    <button @click="sidebarOpen = false" class="mb-4">Close</button>
                    @role('admin')
                        <a href="/admin/dashboard" class="block px-4 py-2 hover:bg-gray-200 dark:hover:bg-gray-700 rounded">Admin Dashboard</a>
                    @endrole
                    @permission('view members')
                        <a href="/members" class="block px-4 py-2 hover:bg-gray-200 dark:hover:bg-gray-700 rounded">Members</a>
                    @endpermission
                    @permission('view loans')
                        <a href="/loans" class="block px-4 py-2 hover:bg-gray-200 dark:hover:bg-gray-700 rounded">Loans</a>
                    @endpermission
                    @permission('view savings')
                        <a href="/savings" class="block px-4 py-2 hover:bg-gray-200 dark:hover:bg-gray-700 rounded">Savings</a>
                    @endpermission
                </div>
            </aside>
            <!-- Header -->
            <header class="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 flex items-center justify-between px-6 py-4">
                <div>
                    @yield('breadcrumbs')
                </div>
                <div class="flex items-center space-x-4">
                    <button @click="dark = !dark; localStorage.setItem('theme', dark ? 'dark' : 'light')" class="focus:outline-none">
                        <svg x-show="!dark" class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m8.66-8.66l-.71.71M4.05 19.07l-.71.71M21 12h-1M4 12H3m16.66-4.66l-.71-.71M4.05 4.93l-.71-.71"/></svg>
                        <svg x-show="dark" class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.293 17.293A8 8 0 016.707 6.707a8.001 8.001 0 1010.586 10.586z"/></svg>
                    </button>
                    <div x-data="{ open: false }" class="relative">
                        <button @click="open = !open" class="focus:outline-none">Profile</button>
                        <div x-show="open" @click.away="open = false" class="absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded shadow z-50">
                            <a href="/profile" class="block px-4 py-2 hover:bg-gray-200 dark:hover:bg-gray-700">Profile</a>
                            <form method="POST" action="{{ route('logout') }}">@csrf<button type="submit" class="w-full text-left px-4 py-2 hover:bg-gray-200 dark:hover:bg-gray-700">Logout</button></form>
                        </div>
                    </div>
                </div>
            </header>
            <!-- Main Content -->
            <main class="p-6">
                <x-toast />
                @yield('content')
            </main>
            <!-- Footer -->
            <footer class="bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 text-center py-4">
                <span>&copy; {{ date('Y') }} {{ config('app.name', 'Sonali') }}. All rights reserved.</span>
            </footer>
        </div>
        <script>
            if ('serviceWorker' in navigator) {
                window.addEventListener('load', function() {
                    navigator.serviceWorker.register('/service-worker.js');
                });
            }
        </script>
    </body>
</html>
