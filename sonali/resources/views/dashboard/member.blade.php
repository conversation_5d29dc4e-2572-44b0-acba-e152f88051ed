@extends('layouts.app')

@section('breadcrumbs')
<x-breadcrumbs :links="[
    ['url' => '/dashboard', 'label' => __('messages.dashboard')],
    ['url' => '/dashboard/member', 'label' => 'Member'],
]" />
@endsection

@section('content')
<div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
    <div class="bg-white dark:bg-gray-800 rounded shadow p-6">
        <h3 class="text-lg font-semibold mb-2">Account Status</h3>
        <div class="flex flex-col space-y-2">
            <div><span class="font-semibold">Member ID:</span> {{ $member->member_id }}</div>
            <div><span class="font-semibold">Status:</span> <span class="px-2 py-1 rounded {{ $member->kyc_status === 'approved' ? 'bg-green-200 text-green-800' : 'bg-yellow-200 text-yellow-800' }}">{{ ucfirst($member->kyc_status) }}</span></div>
            <div><span class="font-semibold">Savings Balance:</span> {{ $member->savings_balance ?? 0 }}</div>
            <div><span class="font-semibold">Loan Balance:</span> {{ $member->loan_balance ?? 0 }}</div>
        </div>
    </div>
    <div class="bg-white dark:bg-gray-800 rounded shadow p-6">
        <h3 class="text-lg font-semibold mb-2">Transaction History</h3>
        <ul class="divide-y divide-gray-200 dark:divide-gray-700">
            @forelse($transactions as $tx)
                <li class="py-2 flex justify-between">
                    <span>{{ $tx['date'] }}</span>
                    <span>{{ $tx['type'] }}</span>
                    <span>{{ $tx['amount'] }}</span>
                </li>
            @empty
                <li class="py-2 text-gray-400">No transactions found.</li>
            @endforelse
        </ul>
    </div>
</div>
<div class="bg-white dark:bg-gray-800 rounded shadow p-6 mb-6">
    <h3 class="text-lg font-semibold mb-4">Savings & Loan Overview</h3>
    <div x-data="{ loading: true }" x-init="
        fetch('/api/member/overview').then(r => r.json()).then(data => {
            loading = false;
            window.renderMemberOverviewChart(data);
        });
    ">
        <x-skeleton height="300px" x-show="loading" />
        <canvas id="memberOverviewChart" x-show="!loading" height="300"></canvas>
    </div>
</div>
<script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.umd.min.js"></script>
<script>
window.renderMemberOverviewChart = function(data) {
    const ctx = document.getElementById('memberOverviewChart').getContext('2d');
    new Chart(ctx, {
        type: 'line',
        data: {
            labels: data.labels,
            datasets: data.datasets
        },
        options: {
            responsive: true,
            plugins: {
                legend: { display: true },
                title: { display: true, text: 'Savings & Loan Overview' }
            },
            scales: {
                x: { display: true },
                y: { display: true }
            }
        }
    });
}
</script>
@endsection 