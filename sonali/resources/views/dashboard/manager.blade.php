@extends('layouts.app')

@section('breadcrumbs')
<x-breadcrumbs :links="[
    ['url' => '/dashboard', 'label' => __('messages.dashboard')],
    ['url' => '/dashboard/manager', 'label' => 'Branch Manager'],
]" />
@endsection

@section('content')
<div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
    <div class="bg-white dark:bg-gray-800 rounded shadow p-6 flex flex-col items-center">
        <div class="text-3xl font-bold text-blue-600">{{ $branchStats['members'] ?? 0 }}</div>
        <div class="text-gray-500 mt-2">Branch Members</div>
    </div>
    <div class="bg-white dark:bg-gray-800 rounded shadow p-6 flex flex-col items-center">
        <div class="text-3xl font-bold text-green-600">{{ $branchStats['loans'] ?? 0 }}</div>
        <div class="text-gray-500 mt-2">Active Loans</div>
    </div>
    <div class="bg-white dark:bg-gray-800 rounded shadow p-6 flex flex-col items-center">
        <div class="text-3xl font-bold text-yellow-600">{{ $branchStats['collections'] ?? 0 }}</div>
        <div class="text-gray-500 mt-2">Collections</div>
    </div>
    <div class="bg-white dark:bg-gray-800 rounded shadow p-6 flex flex-col items-center">
        <div class="text-3xl font-bold text-purple-600">{{ $branchStats['expenses'] ?? 0 }}</div>
        <div class="text-gray-500 mt-2">Expenses</div>
    </div>
</div>
<div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
    <div class="bg-white dark:bg-gray-800 rounded shadow p-6">
        <h3 class="text-lg font-semibold mb-4">Branch Performance Overview</h3>
        <div x-data="{ loading: true }" x-init="
            fetch('/api/manager/branch-performance').then(r => r.json()).then(data => {
                loading = false;
                window.renderManagerBranchPerformanceChart(data);
            });
        ">
            <x-skeleton height="300px" x-show="loading" />
            <canvas id="managerBranchPerformanceChart" x-show="!loading" height="300"></canvas>
        </div>
    </div>
    <div class="bg-white dark:bg-gray-800 rounded shadow p-6">
        <h3 class="text-lg font-semibold mb-4">Field Officer Performance</h3>
        <div x-data="{ loading: true }" x-init="
            fetch('/api/manager/field-officers').then(r => r.json()).then(data => {
                loading = false;
                window.renderFieldOfficerPerformanceChart(data);
            });
        ">
            <x-skeleton height="300px" x-show="loading" />
            <canvas id="fieldOfficerPerformanceChart" x-show="!loading" height="300"></canvas>
        </div>
    </div>
</div>
<div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
    <div class="bg-white dark:bg-gray-800 rounded shadow p-6">
        <h3 class="text-lg font-semibold mb-4">Loan Portfolio</h3>
        <ul class="divide-y divide-gray-200 dark:divide-gray-700">
            @foreach($loanPortfolio as $loan)
                <li class="py-2 flex justify-between">
                    <span>{{ $loan['type'] }}</span>
                    <span>{{ $loan['count'] }}</span>
                    <span>{{ $loan['amount'] }}</span>
                </li>
            @endforeach
        </ul>
    </div>
    <div class="bg-white dark:bg-gray-800 rounded shadow p-6">
        <h3 class="text-lg font-semibold mb-4">Branch Target Tracking</h3>
        <ul class="divide-y divide-gray-200 dark:divide-gray-700">
            @foreach($targets as $target)
                <li class="py-2 flex justify-between">
                    <span>{{ $target['name'] }}</span>
                    <span>{{ $target['current'] }}/{{ $target['goal'] }}</span>
                    <span class="text-xs {{ $target['current'] >= $target['goal'] ? 'text-green-600' : 'text-yellow-600' }}">{{ $target['current'] >= $target['goal'] ? 'Achieved' : 'In Progress' }}</span>
                </li>
            @endforeach
        </ul>
    </div>
</div>
<div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
    <div class="bg-white dark:bg-gray-800 rounded shadow p-6">
        <h3 class="text-lg font-semibold mb-4">Team Management</h3>
        <ul class="divide-y divide-gray-200 dark:divide-gray-700">
            @foreach($team as $officer)
                <li class="py-2 flex justify-between items-center">
                    <span>{{ $officer['name'] }}</span>
                    <span class="text-xs text-gray-500">{{ $officer['role'] }}</span>
                    <span class="text-xs text-blue-600">{{ $officer['performance'] }}</span>
                    <a href="/users/{{ $officer['id'] }}" class="text-blue-600 hover:underline">View</a>
                </li>
            @endforeach
        </ul>
    </div>
    <div class="bg-white dark:bg-gray-800 rounded shadow p-6">
        <h3 class="text-lg font-semibold mb-4">Branch Calendar</h3>
        <div class="overflow-x-auto">
            <table class="min-w-full text-sm">
                <thead>
                    <tr class="bg-gray-100 dark:bg-gray-700">
                        <th class="px-2 py-1">Date</th>
                        <th class="px-2 py-1">Event</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($calendar as $event)
                        <tr>
                            <td class="px-2 py-1">{{ $event['date'] }}</td>
                            <td class="px-2 py-1">{{ $event['event'] }}</td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    </div>
</div>
<div class="bg-white dark:bg-gray-800 rounded shadow p-6 mb-6">
    <h3 class="text-lg font-semibold mb-4">Branch Analytics & Reports</h3>
    <div x-data="{ loading: true }" x-init="
        fetch('/api/manager/branch-analytics').then(r => r.json()).then(data => {
            loading = false;
            window.renderManagerBranchAnalyticsChart(data);
        });
    ">
        <x-skeleton height="300px" x-show="loading" />
        <canvas id="managerBranchAnalyticsChart" x-show="!loading" height="300"></canvas>
    </div>
</div>
<div class="bg-white dark:bg-gray-800 rounded shadow p-6">
    <h3 class="text-lg font-semibold mb-4">Branch Notifications</h3>
    <ul class="space-y-2">
        @forelse($notifications as $note)
            <li class="text-gray-700 dark:text-gray-200">{{ $note }}</li>
        @empty
            <li class="text-gray-400">No notifications</li>
        @endforelse
    </ul>
</div>
<script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.umd.min.js"></script>
<script>
window.renderManagerBranchPerformanceChart = function(data) {
    const ctx = document.getElementById('managerBranchPerformanceChart').getContext('2d');
    new Chart(ctx, { type: 'line', data: { labels: data.labels, datasets: data.datasets }, options: { responsive: true, plugins: { legend: { display: true }, title: { display: true, text: 'Branch Performance' } }, scales: { x: { display: true }, y: { display: true } } } });
}
window.renderFieldOfficerPerformanceChart = function(data) {
    const ctx = document.getElementById('fieldOfficerPerformanceChart').getContext('2d');
    new Chart(ctx, { type: 'bar', data: { labels: data.labels, datasets: data.datasets }, options: { responsive: true, plugins: { legend: { display: true }, title: { display: true, text: 'Field Officer Performance' } }, scales: { x: { display: true }, y: { display: true } } } });
}
window.renderManagerBranchAnalyticsChart = function(data) {
    const ctx = document.getElementById('managerBranchAnalyticsChart').getContext('2d');
    new Chart(ctx, { type: 'bar', data: { labels: data.labels, datasets: data.datasets }, options: { responsive: true, plugins: { legend: { display: true }, title: { display: true, text: 'Branch Analytics' } }, scales: { x: { display: true }, y: { display: true } } } });
}
</script>
@endsection 