@extends('layouts.app')

@section('breadcrumbs')
<x-breadcrumbs :links="[
    ['url' => '/dashboard', 'label' => __('messages.dashboard')],
    ['url' => '/dashboard/field-officer', 'label' => 'Field Officer'],
]" />
@endsection

@section('content')
<div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
    <div class="bg-white dark:bg-gray-800 rounded shadow p-6 flex flex-col items-center">
        <div class="text-3xl font-bold text-blue-600">{{ $stats['members_added'] ?? 0 }}</div>
        <div class="text-gray-500 mt-2">Members Added</div>
    </div>
    <div class="bg-white dark:bg-gray-800 rounded shadow p-6 flex flex-col items-center">
        <div class="text-3xl font-bold text-green-600">{{ $stats['loans_disbursed'] ?? 0 }}</div>
        <div class="text-gray-500 mt-2">Loans Disbursed</div>
    </div>
    <div class="bg-white dark:bg-gray-800 rounded shadow p-6 flex flex-col items-center">
        <div class="text-3xl font-bold text-yellow-600">{{ $stats['collections'] ?? 0 }}</div>
        <div class="text-gray-500 mt-2">Collections</div>
    </div>
</div>
<div class="bg-white dark:bg-gray-800 rounded shadow p-6 mb-6">
    <h3 class="text-lg font-semibold mb-4">Performance Metrics</h3>
    <div x-data="{ loading: true }" x-init="
        fetch('/api/field-officer/metrics').then(r => r.json()).then(data => {
            loading = false;
            window.renderFieldOfficerChart(data);
        });
    ">
        <x-skeleton height="300px" x-show="loading" />
        <canvas id="fieldOfficerChart" x-show="!loading" height="300"></canvas>
    </div>
</div>
<script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.umd.min.js"></script>
<script>
window.renderFieldOfficerChart = function(data) {
    const ctx = document.getElementById('fieldOfficerChart').getContext('2d');
    new Chart(ctx, {
        type: 'line',
        data: {
            labels: data.labels,
            datasets: data.datasets
        },
        options: {
            responsive: true,
            plugins: {
                legend: { display: true },
                title: { display: true, text: 'Monthly Performance' }
            },
            scales: {
                x: { display: true },
                y: { display: true }
            }
        }
    });
}
 