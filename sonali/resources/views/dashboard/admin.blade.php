@extends('layouts.app')

@section('breadcrumbs')
<x-breadcrumbs :links="[
    ['url' => '/admin/dashboard', 'label' => __('messages.dashboard')],
]" />
@endsection

@section('content')
<div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
    <div class="bg-white dark:bg-gray-800 rounded shadow p-6 flex flex-col items-center">
        <div class="text-3xl font-bold text-blue-600">{{ $stats['members'] ?? 0 }}</div>
        <div class="text-gray-500 mt-2">{{ __('messages.members') }}</div>
    </div>
    <div class="bg-white dark:bg-gray-800 rounded shadow p-6 flex flex-col items-center">
        <div class="text-3xl font-bold text-green-600">{{ $stats['loans'] ?? 0 }}</div>
        <div class="text-gray-500 mt-2">{{ __('messages.loans') }}</div>
    </div>
    <div class="bg-white dark:bg-gray-800 rounded shadow p-6 flex flex-col items-center">
        <div class="text-3xl font-bold text-yellow-600">{{ $stats['savings'] ?? 0 }}</div>
        <div class="text-gray-500 mt-2">{{ __('messages.savings') }}</div>
    </div>
    <div class="bg-white dark:bg-gray-800 rounded shadow p-6 flex flex-col items-center">
        <div class="text-3xl font-bold text-purple-600">{{ $stats['branches'] ?? 0 }}</div>
        <div class="text-gray-500 mt-2">Branches</div>
    </div>
</div>
<div class="flex flex-wrap gap-4 mb-6">
    <button class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700" accesskey="n">+ New Member <span class="text-xs">(Alt+N)</span></button>
    <button class="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700" accesskey="l">+ New Loan <span class="text-xs">(Alt+L)</span></button>
    <button class="bg-yellow-600 text-white px-4 py-2 rounded hover:bg-yellow-700" accesskey="s">+ New Savings <span class="text-xs">(Alt+S)</span></button>
    <button class="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700" accesskey="e">Export Data</button>
</div>
<div class="bg-white dark:bg-gray-800 rounded shadow p-6 mb-6">
    <h3 class="text-lg font-semibold mb-4">System Analytics</h3>
    <div x-data="{ loading: true }" x-init="
        fetch('/api/admin/analytics').then(r => r.json()).then(data => {
            loading = false;
            window.renderAdminAnalyticsChart(data);
        });
    ">
        <x-skeleton height="300px" x-show="loading" />
        <canvas id="adminAnalyticsChart" x-show="!loading" height="300"></canvas>
    </div>
</div>
<div class="grid grid-cols-1 md:grid-cols-2 gap-6">
    <div class="bg-white dark:bg-gray-800 rounded shadow p-6">
        <h3 class="text-lg font-semibold mb-4">Quick Links</h3>
        <ul class="space-y-2">
            <li><a href="/members" class="text-blue-600 hover:underline">Manage Members</a></li>
            <li><a href="/loans" class="text-blue-600 hover:underline">Manage Loans</a></li>
            <li><a href="/savings" class="text-blue-600 hover:underline">Manage Savings</a></li>
        </ul>
    </div>
    <div class="bg-white dark:bg-gray-800 rounded shadow p-6">
        <h3 class="text-lg font-semibold mb-4">Notifications</h3>
        <ul class="space-y-2">
            @forelse($notifications as $note)
                <li class="text-gray-700 dark:text-gray-200">{{ $note }}</li>
            @empty
                <li class="text-gray-400">No notifications</li>
            @endforelse
        </ul>
    </div>
</div>
<script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.umd.min.js"></script>
<script>
window.renderAdminAnalyticsChart = function(data) {
    const ctx = document.getElementById('adminAnalyticsChart').getContext('2d');
    new Chart(ctx, {
        type: 'bar',
        data: {
            labels: data.labels,
            datasets: data.datasets
        },
        options: {
            responsive: true,
            plugins: {
                legend: { display: true },
                title: { display: true, text: 'System Analytics' }
            },
            scales: {
                x: { display: true },
                y: { display: true }
            }
        }
    });
}
</script>
@endsection 