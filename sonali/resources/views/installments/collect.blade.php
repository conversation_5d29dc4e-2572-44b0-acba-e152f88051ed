@extends('layouts.app')

@section('breadcrumbs')
<x-breadcrumbs :links="[
    ['url' => '/installments', 'label' => 'Installments'],
    ['url' => '/installments/collect', 'label' => 'Collect Installment'],
]" />
@endsection

@section('content')
<div class="max-w-3xl mx-auto p-6 bg-white dark:bg-gray-800 rounded shadow mt-8">
    <h2 class="text-2xl font-bold mb-4">Installment Collection</h2>
    <x-error-boundary>
    <form method="GET" action="{{ route('installments.collect') }}" class="mb-6 flex gap-2">
        <input type="text" name="q" value="{{ request('q') }}" placeholder="Search by Member ID or Name" class="w-full border rounded px-3 py-2" />
        <button type="submit" class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">Search</button>
    </form>
    @if(isset($member))
        <div class="mb-4">
            <div class="font-semibold">Member: {{ $member->name }} ({{ $member->member_id }})</div>
            <div class="text-sm text-gray-500">Phone: {{ $member->phone_number }}</div>
        </div>
        <div class="overflow-x-auto">
            <table class="min-w-full text-sm">
                <thead>
                    <tr class="bg-gray-100 dark:bg-gray-700">
                        <th class="px-2 py-1">#</th>
                        <th class="px-2 py-1">Due Date</th>
                        <th class="px-2 py-1">Amount</th>
                        <th class="px-2 py-1">Paid</th>
                        <th class="px-2 py-1">Due</th>
                        <th class="px-2 py-1">Status</th>
                        <th class="px-2 py-1">Action</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($installments as $inst)
                        <tr class="{{ $inst->status === 'overdue' ? 'bg-red-100 dark:bg-red-900' : '' }}">
                            <td class="px-2 py-1">{{ $inst->installment_no }}</td>
                            <td class="px-2 py-1">{{ $inst->installment_date->format('Y-m-d') }}</td>
                            <td class="px-2 py-1">{{ $inst->installment_amount }}</td>
                            <td class="px-2 py-1">{{ $inst->advance_paid }}</td>
                            <td class="px-2 py-1">{{ $inst->due_amount }}</td>
                            <td class="px-2 py-1">
                                @if($inst->status === 'paid')
                                    <span class="text-green-600">Paid</span>
                                @elseif($inst->status === 'overdue')
                                    <span class="text-red-600">Overdue</span>
                                @else
                                    <span class="text-yellow-600">Pending</span>
                                @endif
                            </td>
                            <td class="px-2 py-1">
                                @if($inst->status !== 'paid')
                                <form method="POST" action="{{ route('installments.pay', $inst) }}" class="flex items-center gap-2" x-data="{ loading: false, amount: {{ $inst->due_amount }} }" @submit.prevent="loading = true; $el.submit()">
                                    @csrf
                                    <input type="number" name="amount" min="1" :max="{{ $inst->due_amount }}" x-model.number="amount" class="w-20 border rounded px-2 py-1" />
                                    <button type="submit" class="bg-green-600 text-white px-2 py-1 rounded hover:bg-green-700 flex items-center">
                                        <span x-show="!loading">Collect</span>
                                        <x-skeleton width="1rem" height="1rem" x-show="loading" />
                                    </button>
                                </form>
                                @else
                                <a href="{{ route('installments.receipt', $inst) }}" target="_blank" class="text-blue-600 hover:underline">Receipt</a>
                                @endif
                            </td>
                        </tr>
                    @empty
                        <tr><td colspan="7" class="text-center text-gray-400 py-4">No installments found.</td></tr>
                    @endforelse
                </tbody>
            </table>
        </div>
        <div class="mt-6">
            <a href="{{ route('installments.history', $member) }}" class="text-blue-600 hover:underline">View Installment History</a>
        </div>
    @elseif(request('q'))
        <div class="text-red-600">No member found for your search.</div>
    @endif
    </x-error-boundary>
</div>
@endsection 