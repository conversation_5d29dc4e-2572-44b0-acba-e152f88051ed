@extends('layouts.app')

@section('breadcrumbs')
<x-breadcrumbs :links="[
    ['url' => '/finance', 'label' => 'Branch Finance'],
    ['url' => '/finance/dashboard', 'label' => 'Dashboard'],
]" />
@endsection

@section('content')
<div class="max-w-6xl mx-auto p-6 bg-white dark:bg-gray-800 rounded shadow mt-8">
    <h2 class="text-2xl font-bold mb-4">Branch Financial Dashboard</h2>
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div class="bg-green-100 dark:bg-green-900 p-4 rounded">
            <div class="text-sm text-gray-500">Total Income</div>
            <div class="text-2xl font-bold">৳{{ number_format($summary['income'], 2) }}</div>
        </div>
        <div class="bg-red-100 dark:bg-red-900 p-4 rounded">
            <div class="text-sm text-gray-500">Total Expenditure</div>
            <div class="text-2xl font-bold">৳{{ number_format($summary['expenditure'], 2) }}</div>
        </div>
        <div class="bg-blue-100 dark:bg-blue-900 p-4 rounded">
            <div class="text-sm text-gray-500">Budget Used</div>
            <div class="text-2xl font-bold">{{ $summary['budget_used'] }}%</div>
        </div>
        <div class="bg-yellow-100 dark:bg-yellow-900 p-4 rounded">
            <div class="text-sm text-gray-500">Cash Flow</div>
            <div class="text-2xl font-bold">৳{{ number_format($summary['cash_flow'], 2) }}</div>
        </div>
    </div>
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <div class="bg-white dark:bg-gray-900 p-4 rounded shadow">
            <h3 class="font-semibold mb-2">Recent Transactions</h3>
            <ul class="divide-y divide-gray-200 dark:divide-gray-700">
                @foreach($transactions as $txn)
                    <li class="py-2 flex justify-between items-center">
                        <span>{{ $txn->date }} - {{ $txn->description }}</span>
                        <span class="font-bold {{ $txn->type === 'income' ? 'text-green-600' : 'text-red-600' }}">৳{{ number_format($txn->amount, 2) }}</span>
                    </li>
                @endforeach
            </ul>
        </div>
        <div class="bg-white dark:bg-gray-900 p-4 rounded shadow">
            <h3 class="font-semibold mb-2">Expense Categories</h3>
            <canvas id="expenseCategoryChart" height="180"></canvas>
        </div>
    </div>
    <div class="bg-white dark:bg-gray-900 p-4 rounded shadow mb-6">
        <h3 class="font-semibold mb-2">Cash Flow</h3>
        <canvas id="cashFlowChart" height="100"></canvas>
    </div>
    <div class="bg-white dark:bg-gray-900 p-4 rounded shadow mb-6">
        <h3 class="font-semibold mb-2">Budget Tracking</h3>
        <div class="w-full bg-gray-200 rounded-full h-4 mb-2">
            <div class="bg-blue-600 h-4 rounded-full" style="width: {{ $summary['budget_used'] }}%"></div>
        </div>
        <div class="flex justify-between text-xs text-gray-500">
            <span>Used: ৳{{ number_format($summary['budget_spent'], 2) }}</span>
            <span>Budget: ৳{{ number_format($summary['budget_total'], 2) }}</span>
        </div>
    </div>
    <div class="bg-white dark:bg-gray-900 p-4 rounded shadow">
        <h3 class="font-semibold mb-2">Pending Approvals</h3>
        <ul class="divide-y divide-gray-200 dark:divide-gray-700">
            @forelse($pendingApprovals as $approval)
                <li class="py-2 flex justify-between items-center">
                    <span>{{ $approval->description }}</span>
                    <span class="text-yellow-600 font-semibold">Pending</span>
                </li>
            @empty
                <li class="py-2 text-gray-500">No pending approvals.</li>
            @endforelse
        </ul>
    </div>
</div>
@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    const ctx1 = document.getElementById('expenseCategoryChart').getContext('2d');
    new Chart(ctx1, {
        type: 'doughnut',
        data: {
            labels: @json(array_keys($categorySummary)),
            datasets: [{
                data: @json(array_values($categorySummary)),
                backgroundColor: ['#f87171', '#60a5fa', '#34d399', '#fbbf24', '#a78bfa', '#f472b6'],
            }]
        },
        options: {responsive: true, plugins: {legend: {position: 'bottom'}}}
    });
    const ctx2 = document.getElementById('cashFlowChart').getContext('2d');
    new Chart(ctx2, {
        type: 'line',
        data: {
            labels: @json($cashFlowLabels),
            datasets: [{
                label: 'Cash Flow',
                data: @json($cashFlowData),
                borderColor: '#2563eb',
                backgroundColor: 'rgba(37,99,235,0.1)',
                fill: true,
            }]
        },
        options: {responsive: true, plugins: {legend: {display: false}}}
    });
</script>
@endpush
@endsection 