@extends('layouts.app')

@section('breadcrumbs')
<x-breadcrumbs :links="[
    ['url' => '/finance', 'label' => 'Branch Finance'],
    ['url' => '/finance/transactions', 'label' => 'Transactions'],
    ['url' => '#', 'label' => 'New Entry'],
]" />
@endsection

@section('content')
<div class="max-w-lg mx-auto p-6 bg-white dark:bg-gray-800 rounded shadow mt-8">
    <h2 class="text-2xl font-bold mb-4">New Transaction Entry</h2>
    <form method="POST" action="{{ route('finance.transactions.store') }}" class="space-y-4">
        @csrf
        <div>
            <label class="block text-sm font-medium mb-1">Date</label>
            <input type="date" name="date" value="{{ old('date') }}" class="w-full border rounded px-3 py-2" required />
            @error('date') <div class="text-red-600 text-xs">{{ $message }}</div> @enderror
        </div>
        <div>
            <label class="block text-sm font-medium mb-1">Amount</label>
            <input type="number" step="0.01" name="amount" value="{{ old('amount') }}" class="w-full border rounded px-3 py-2" required />
            @error('amount') <div class="text-red-600 text-xs">{{ $message }}</div> @enderror
        </div>
        <div>
            <label class="block text-sm font-medium mb-1">Type</label>
            <select name="type" class="w-full border rounded px-3 py-2" required>
                <option value="">Select</option>
                <option value="income" @selected(old('type')==='income')>Income</option>
                <option value="expenditure" @selected(old('type')==='expenditure')>Expenditure</option>
            </select>
            @error('type') <div class="text-red-600 text-xs">{{ $message }}</div> @enderror
        </div>
        <div>
            <label class="block text-sm font-medium mb-1">Category</label>
            <select name="category" class="w-full border rounded px-3 py-2" required>
                <option value="">Select</option>
                @foreach($categories as $cat)
                    <option value="{{ $cat }}" @selected(old('category')===$cat)> {{ $cat }} </option>
                @endforeach
            </select>
            @error('category') <div class="text-red-600 text-xs">{{ $message }}</div> @enderror
        </div>
        <div>
            <label class="block text-sm font-medium mb-1">Description</label>
            <input type="text" name="description" value="{{ old('description') }}" class="w-full border rounded px-3 py-2" />
            @error('description') <div class="text-red-600 text-xs">{{ $message }}</div> @enderror
        </div>
        <div>
            <label class="block text-sm font-medium mb-1">Approval Status</label>
            <select name="approval_status" class="w-full border rounded px-3 py-2" required>
                <option value="pending" @selected(old('approval_status')==='pending')>Pending</option>
                <option value="approved" @selected(old('approval_status')==='approved')>Approved</option>
                <option value="rejected" @selected(old('approval_status')==='rejected')>Rejected</option>
            </select>
            @error('approval_status') <div class="text-red-600 text-xs">{{ $message }}</div> @enderror
        </div>
        <div class="flex justify-end">
            <button type="submit" class="bg-blue-600 text-white px-6 py-2 rounded hover:bg-blue-700">Save Transaction</button>
        </div>
    </form>
</div>
@endsection 