@extends('layouts.app')

@section('breadcrumbs')
<x-breadcrumbs :links="[
    ['url' => '/members', 'label' => __('messages.members')],
    ['url' => '/members/create', 'label' => 'Register Member'],
]" />
@endsection

@section('content')
<div class="max-w-2xl mx-auto p-6 bg-white dark:bg-gray-800 rounded shadow mt-8">
    <h2 class="text-2xl font-bold mb-4">Register New Member</h2>
    <x-error-boundary>
    <form method="POST" action="{{ route('members.store') }}" enctype="multipart/form-data" class="space-y-4" x-data="{ loading: false, photoPreview: null }" @submit.prevent="loading = true; $el.submit()">
        @csrf
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
                <label for="name" class="block text-sm font-medium">Name</label>
                <input id="name" type="text" name="name" value="{{ old('name') }}" required class="w-full border rounded px-3 py-2 mt-1 @error('name') border-red-500 @enderror">
                @error('name')<span class="text-red-500 text-xs">{{ $message }}</span>@enderror
            </div>
            <div>
                <label for="father_or_husband_name" class="block text-sm font-medium">Father/Husband Name</label>
                <input id="father_or_husband_name" type="text" name="father_or_husband_name" value="{{ old('father_or_husband_name') }}" class="w-full border rounded px-3 py-2 mt-1 @error('father_or_husband_name') border-red-500 @enderror">
                @error('father_or_husband_name')<span class="text-red-500 text-xs">{{ $message }}</span>@enderror
            </div>
            <div>
                <label for="mother_name" class="block text-sm font-medium">Mother Name</label>
                <input id="mother_name" type="text" name="mother_name" value="{{ old('mother_name') }}" class="w-full border rounded px-3 py-2 mt-1 @error('mother_name') border-red-500 @enderror">
                @error('mother_name')<span class="text-red-500 text-xs">{{ $message }}</span>@enderror
            </div>
            <div>
                <label for="present_address" class="block text-sm font-medium">Present Address</label>
                <input id="present_address" type="text" name="present_address" value="{{ old('present_address') }}" class="w-full border rounded px-3 py-2 mt-1 @error('present_address') border-red-500 @enderror">
                @error('present_address')<span class="text-red-500 text-xs">{{ $message }}</span>@enderror
            </div>
            <div>
                <label for="permanent_address" class="block text-sm font-medium">Permanent Address</label>
                <input id="permanent_address" type="text" name="permanent_address" value="{{ old('permanent_address') }}" class="w-full border rounded px-3 py-2 mt-1 @error('permanent_address') border-red-500 @enderror">
                @error('permanent_address')<span class="text-red-500 text-xs">{{ $message }}</span>@enderror
            </div>
            <div>
                <label for="nid_number" class="block text-sm font-medium">NID Number</label>
                <input id="nid_number" type="text" name="nid_number" value="{{ old('nid_number') }}" required class="w-full border rounded px-3 py-2 mt-1 @error('nid_number') border-red-500 @enderror">
                @error('nid_number')<span class="text-red-500 text-xs">{{ $message }}</span>@enderror
            </div>
            <div>
                <label for="date_of_birth" class="block text-sm font-medium">Date of Birth</label>
                <input id="date_of_birth" type="date" name="date_of_birth" value="{{ old('date_of_birth') }}" class="w-full border rounded px-3 py-2 mt-1 @error('date_of_birth') border-red-500 @enderror">
                @error('date_of_birth')<span class="text-red-500 text-xs">{{ $message }}</span>@enderror
            </div>
            <div>
                <label for="religion" class="block text-sm font-medium">Religion</label>
                <input id="religion" type="text" name="religion" value="{{ old('religion') }}" class="w-full border rounded px-3 py-2 mt-1 @error('religion') border-red-500 @enderror">
                @error('religion')<span class="text-red-500 text-xs">{{ $message }}</span>@enderror
            </div>
            <div>
                <label for="phone_number" class="block text-sm font-medium">Phone Number</label>
                <input id="phone_number" type="text" name="phone_number" value="{{ old('phone_number') }}" required class="w-full border rounded px-3 py-2 mt-1 @error('phone_number') border-red-500 @enderror">
                @error('phone_number')<span class="text-red-500 text-xs">{{ $message }}</span>@enderror
            </div>
            <div>
                <label for="blood_group" class="block text-sm font-medium">Blood Group</label>
                <input id="blood_group" type="text" name="blood_group" value="{{ old('blood_group') }}" class="w-full border rounded px-3 py-2 mt-1 @error('blood_group') border-red-500 @enderror">
                @error('blood_group')<span class="text-red-500 text-xs">{{ $message }}</span>@enderror
            </div>
            <div>
                <label for="occupation" class="block text-sm font-medium">Occupation</label>
                <input id="occupation" type="text" name="occupation" value="{{ old('occupation') }}" class="w-full border rounded px-3 py-2 mt-1 @error('occupation') border-red-500 @enderror">
                @error('occupation')<span class="text-red-500 text-xs">{{ $message }}</span>@enderror
            </div>
            <div>
                <label for="reference_id" class="block text-sm font-medium">Reference Member</label>
                <select id="reference_id" name="reference_id" class="w-full border rounded px-3 py-2 mt-1">
                    <option value="">None</option>
                    @foreach($members as $ref)
                        <option value="{{ $ref->id }}" @if(old('reference_id') == $ref->id) selected @endif>{{ $ref->name }} ({{ $ref->member_id }})</option>
                    @endforeach
                </select>
                @error('reference_id')<span class="text-red-500 text-xs">{{ $message }}</span>@enderror
            </div>
            <div>
                <label for="photo" class="block text-sm font-medium">Photo</label>
                <input id="photo" type="file" name="photo" accept="image/*" class="w-full border rounded px-3 py-2 mt-1 @error('photo') border-red-500 @enderror" @change="photoPreview = $event.target.files[0] ? URL.createObjectURL($event.target.files[0]) : null">
                <template x-if="photoPreview">
                    <img :src="photoPreview" class="mt-2 w-24 h-24 object-cover rounded border" alt="Preview">
                </template>
                @error('photo')<span class="text-red-500 text-xs">{{ $message }}</span>@enderror
            </div>
        </div>
        <div class="flex justify-end mt-4">
            <button type="submit" class="bg-blue-600 text-white px-6 py-2 rounded hover:bg-blue-700 flex items-center justify-center">
                <span x-show="!loading">Register Member</span>
                <x-skeleton width="1.5rem" height="1.5rem" x-show="loading" />
            </button>
        </div>
    </form>
    </x-error-boundary>
</div>
@endsection 