@extends('layouts.app')

@section('breadcrumbs')
<x-breadcrumbs :links="[
    ['url' => '/loans', 'label' => 'Loans'],
    ['url' => '/loans/create', 'label' => 'Apply for Loan'],
]" />
@endsection

@section('content')
<div class="max-w-2xl mx-auto p-6 bg-white dark:bg-gray-800 rounded shadow mt-8">
    <h2 class="text-2xl font-bold mb-4">Loan Application</h2>
    <x-error-boundary>
    <form method="POST" action="{{ route('loans.store') }}" class="space-y-4" x-data="loanCalculator()" @submit.prevent="loading = true; $el.submit()">
        @csrf
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
                <label for="member_id" class="block text-sm font-medium">Member</label>
                <select id="member_id" name="member_id" required class="w-full border rounded px-3 py-2 mt-1 @error('member_id') border-red-500 @enderror" x-model="memberId" @change="fetchCycle()">
                    <option value="">Select Member</option>
                    @foreach($members as $member)
                        <option value="{{ $member->id }}" @if(old('member_id') == $member->id) selected @endif>{{ $member->name }} ({{ $member->member_id }})</option>
                    @endforeach
                </select>
                @error('member_id')<span class="text-red-500 text-xs">{{ $message }}</span>@enderror
            </div>
            <div>
                <label for="applied_amount" class="block text-sm font-medium">Applied Amount</label>
                <input id="applied_amount" type="number" name="applied_amount" min="1000" step="100" required class="w-full border rounded px-3 py-2 mt-1 @error('applied_amount') border-red-500 @enderror" x-model.number="appliedAmount" @input="calculate()">
                @error('applied_amount')<span class="text-red-500 text-xs">{{ $message }}</span>@enderror
            </div>
            <div>
                <label for="reason" class="block text-sm font-medium">Reason</label>
                <input id="reason" type="text" name="reason" value="{{ old('reason') }}" class="w-full border rounded px-3 py-2 mt-1 @error('reason') border-red-500 @enderror">
                @error('reason')<span class="text-red-500 text-xs">{{ $message }}</span>@enderror
            </div>
            <div>
                <label for="loan_cycle_number" class="block text-sm font-medium">Loan Cycle</label>
                <input id="loan_cycle_number" type="number" name="loan_cycle_number" readonly class="w-full border rounded px-3 py-2 mt-1 bg-gray-100 dark:bg-gray-700" x-model="loanCycle">
            </div>
            <div>
                <label for="recommender_id" class="block text-sm font-medium">Recommender</label>
                <select id="recommender_id" name="recommender_id" class="w-full border rounded px-3 py-2 mt-1">
                    <option value="">None</option>
                    @foreach($members as $rec)
                        <option value="{{ $rec->id }}" @if(old('recommender_id') == $rec->id) selected @endif>{{ $rec->name }} ({{ $rec->member_id }})</option>
                    @endforeach
                </select>
                @error('recommender_id')<span class="text-red-500 text-xs">{{ $message }}</span>@enderror
            </div>
            <div>
                <label for="advance_payment" class="block text-sm font-medium">Advance Payment</label>
                <input id="advance_payment" type="number" name="advance_payment" min="0" step="100" class="w-full border rounded px-3 py-2 mt-1 @error('advance_payment') border-red-500 @enderror" x-model.number="advancePayment" @input="calculate()">
                @error('advance_payment')<span class="text-red-500 text-xs">{{ $message }}</span>@enderror
            </div>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
            <div>
                <label class="block text-xs font-medium">Installment Count</label>
                <input type="number" name="installment_count" min="1" max="36" class="w-full border rounded px-3 py-2 mt-1" x-model.number="installmentCount" @input="calculate()">
            </div>
            <div>
                <label class="block text-xs font-medium">Installment Amount</label>
                <input type="text" readonly class="w-full border rounded px-3 py-2 mt-1 bg-gray-100 dark:bg-gray-700" :value="installmentAmount">
            </div>
            <div>
                <label class="block text-xs font-medium">Total Repayment</label>
                <input type="text" readonly class="w-full border rounded px-3 py-2 mt-1 bg-gray-100 dark:bg-gray-700" :value="totalRepayment">
            </div>
        </div>
        <div class="flex justify-end mt-4">
            <button type="submit" class="bg-blue-600 text-white px-6 py-2 rounded hover:bg-blue-700 flex items-center justify-center">
                <span x-show="!loading">Submit Application</span>
                <x-skeleton width="1.5rem" height="1.5rem" x-show="loading" />
            </button>
        </div>
    </form>
    </x-error-boundary>
</div>
<script>
function loanCalculator() {
    return {
        loading: false,
        memberId: '',
        appliedAmount: 0,
        advancePayment: 0,
        installmentCount: 12,
        installmentAmount: 0,
        totalRepayment: 0,
        loanCycle: 1,
        calculate() {
            let principal = this.appliedAmount - this.advancePayment;
            let interestRate = 0.12; // 12% annual
            let months = this.installmentCount;
            let monthlyInterest = interestRate / 12;
            let total = principal * Math.pow(1 + monthlyInterest, months);
            this.totalRepayment = total.toFixed(2);
            this.installmentAmount = (total / months).toFixed(2);
        },
        fetchCycle() {
            // Optionally fetch loan cycle from backend
            // For now, just set to 1
            this.loanCycle = 1;
        }
    }
}
</script>
@endsection 