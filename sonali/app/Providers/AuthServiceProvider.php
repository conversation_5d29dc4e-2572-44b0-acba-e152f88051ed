<?php

namespace App\Providers;

use Illuminate\Support\Facades\Gate;
use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;
use App\Models\Branch;
use App\Policies\BranchPolicy;

class AuthServiceProvider extends ServiceProvider
{
    protected $policies = [
        Branch::class => BranchPolicy::class,
        // 'App\Models\Model' => 'App\Policies\ModelPolicy',
        // Add resource policies here
    ];

    public function boot(): void
    {
        $this->registerPolicies();

        // Branch-level access gate
        Gate::define('access-branch', function ($user, $branchId) {
            return $user->branch_id === $branchId || $user->hasRole('admin');
        });
    }
} 