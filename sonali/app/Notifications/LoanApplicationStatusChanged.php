<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;

class LoanApplicationStatusChanged extends Notification implements ShouldQueue
{
    use Queueable;

    public $application;

    public function __construct($application)
    {
        $this->application = $application;
    }

    public function via($notifiable)
    {
        return ['mail', 'database'];
    }

    public function toMail($notifiable)
    {
        return (new MailMessage)
            ->subject('Loan Application Status Updated')
            ->greeting('Hello ' . $notifiable->name)
            ->line('Your loan application status has been updated to: ' . ucfirst($this->application->status))
            ->action('View Application', url('/loans/review?q=' . $this->application->member->member_id))
            ->line('Thank you for using our service!');
    }

    public function toArray($notifiable)
    {
        return [
            'application_id' => $this->application->id,
            'status' => $this->application->status,
            'reviewed_by' => $this->application->reviewed_by,
            'reviewed_at' => $this->application->reviewed_at,
        ];
    }
} 