<?php

namespace App\Services;

use Illuminate\Http\UploadedFile;
use Illuminate\Support\Str;
use Intervention\Image\ImageManagerStatic as Image;
use Illuminate\Support\Facades\Storage;

class MemberPhotoService
{
    public static function store(UploadedFile $file, $memberId): string
    {
        $filename = $memberId . '_' . Str::random(8) . '.webp';
        $sizes = [
            'original' => null,
            'medium' => 300,
            'thumb' => 100,
        ];
        $paths = [];
        foreach ($sizes as $folder => $width) {
            $image = Image::make($file->getRealPath());
            if ($width) {
                $image->resize($width, null, function ($constraint) {
                    $constraint->aspectRatio();
                });
            }
            $path = "members/{$folder}/{$filename}";
            Storage::disk('public')->put($path, (string) $image->encode('webp', 90));
            $paths[$folder] = $path;
        }
        return $paths['original'];
    }
} 