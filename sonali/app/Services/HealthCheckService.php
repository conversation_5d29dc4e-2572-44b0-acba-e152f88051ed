<?php

namespace App\Services;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Redis;
use Exception;

class HealthCheckService
{
    /**
     * Check database connection health
     */
    public static function checkDatabase(): array
    {
        try {
            DB::connection('mysql')->getPdo();
            return [
                'status' => 'healthy',
                'message' => 'Database connection successful',
                'timestamp' => now()->toISOString()
            ];
        } catch (Exception $e) {
            return [
                'status' => 'unhealthy',
                'message' => 'Database connection failed: ' . $e->getMessage(),
                'timestamp' => now()->toISOString()
            ];
        }
    }

    /**
     * Check cache connection health
     */
    public static function checkCache(): array
    {
        try {
            Cache::put('health_check', 'test', 10);
            $value = Cache::get('health_check');
            Cache::forget('health_check');
            
            if ($value === 'test') {
                return [
                    'status' => 'healthy',
                    'message' => 'Cache is working properly',
                    'timestamp' => now()->toISOString()
                ];
            } else {
                return [
                    'status' => 'unhealthy',
                    'message' => 'Cache test failed',
                    'timestamp' => now()->toISOString()
                ];
            }
        } catch (Exception $e) {
            return [
                'status' => 'unhealthy',
                'message' => 'Cache connection failed: ' . $e->getMessage(),
                'timestamp' => now()->toISOString()
            ];
        }
    }

    /**
     * Check Redis connection health
     */
    public static function checkRedis(): array
    {
        try {
            Redis::ping();
            return [
                'status' => 'healthy',
                'message' => 'Redis connection successful',
                'timestamp' => now()->toISOString()
            ];
        } catch (Exception $e) {
            return [
                'status' => 'unhealthy',
                'message' => 'Redis connection failed: ' . $e->getMessage(),
                'timestamp' => now()->toISOString()
            ];
        }
    }

    /**
     * Check queue health
     */
    public static function checkQueue(): array
    {
        try {
            // Check if queue connection is working
            $connection = config('queue.default');
            $queueManager = app('queue');
            $queueManager->connection($connection);
            
            return [
                'status' => 'healthy',
                'message' => 'Queue connection successful',
                'timestamp' => now()->toISOString()
            ];
        } catch (Exception $e) {
            return [
                'status' => 'unhealthy',
                'message' => 'Queue connection failed: ' . $e->getMessage(),
                'timestamp' => now()->toISOString()
            ];
        }
    }

    /**
     * Get overall system health
     */
    public static function getSystemHealth(): array
    {
        $checks = [
            'database' => self::checkDatabase(),
            'cache' => self::checkCache(),
            'redis' => self::checkRedis(),
            'queue' => self::checkQueue(),
        ];

        $overallStatus = 'healthy';
        foreach ($checks as $check) {
            if ($check['status'] === 'unhealthy') {
                $overallStatus = 'unhealthy';
                break;
            }
        }

        return [
            'overall_status' => $overallStatus,
            'checks' => $checks,
            'timestamp' => now()->toISOString(),
            'version' => config('app.version', '1.0.0')
        ];
    }
}
