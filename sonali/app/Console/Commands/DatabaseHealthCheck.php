<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class DatabaseHealthCheck extends Command
{
    protected $signature = 'db:health';
    protected $description = 'Check the health of the MySQL database connection';

    public function handle()
    {
        try {
            DB::connection('mysql')->getPdo();
            $this->info('Database connection is healthy.');
            return 0;
        } catch (\Exception $e) {
            $this->error('Database connection failed: ' . $e->getMessage());
            return 1;
        }
    }
} 