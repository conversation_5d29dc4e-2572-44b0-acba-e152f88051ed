<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class DatabaseBackup extends Command
{
    protected $signature = 'db:backup';
    protected $description = 'Backup the MySQL database to storage/app/backups';

    public function handle()
    {
        $filename = 'backup_' . date('Y_m_d_His') . '_' . Str::random(8) . '.sql';
        $path = storage_path('app/backups');
        if (!is_dir($path)) {
            mkdir($path, 0755, true);
        }
        $file = $path . '/' . $filename;
        $db = env('DB_DATABASE');
        $user = env('DB_USERNAME');
        $pass = env('DB_PASSWORD');
        $host = env('DB_HOST');
        $port = env('DB_PORT', 3306);
        $command = "mysqldump -h{$host} -P{$port} -u{$user} -p'{$pass}' {$db} > {$file}";
        $result = null;
        $output = null;
        exec($command, $output, $result);
        if ($result === 0) {
            $this->info("Backup created: {$file}");
        } else {
            $this->error('Backup failed.');
        }
    }
} 