<?php

namespace App\Http\Controllers;

use App\Services\HealthCheckService;
use Illuminate\Http\JsonResponse;

class HealthController extends Controller
{
    /**
     * Get system health status
     */
    public function index(): JsonResponse
    {
        $health = HealthCheckService::getSystemHealth();
        
        $statusCode = $health['overall_status'] === 'healthy' ? 200 : 503;
        
        return response()->json($health, $statusCode);
    }

    /**
     * Get database health status
     */
    public function database(): JsonResponse
    {
        $health = HealthCheckService::checkDatabase();
        
        $statusCode = $health['status'] === 'healthy' ? 200 : 503;
        
        return response()->json($health, $statusCode);
    }

    /**
     * Get cache health status
     */
    public function cache(): JsonResponse
    {
        $health = HealthCheckService::checkCache();
        
        $statusCode = $health['status'] === 'healthy' ? 200 : 503;
        
        return response()->json($health, $statusCode);
    }

    /**
     * Simple health check endpoint
     */
    public function simple(): JsonResponse
    {
        return response()->json([
            'status' => 'ok',
            'timestamp' => now()->toISOString()
        ]);
    }
}
