<?php

namespace App\Http\Controllers;

use App\Models\BranchTransaction;
use App\Models\Branch;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class BranchTransactionController extends Controller
{
    // Dashboard with summary, analytics, and pending approvals
    public function dashboard()
    {
        $branchId = Auth::user()->branch_id;
        $summary = [
            'income' => BranchTransaction::where('branch_id', $branchId)->where('type', 'income')->sum('amount'),
            'expenditure' => BranchTransaction::where('branch_id', $branchId)->where('type', 'expenditure')->sum('amount'),
            'budget_total' => 100000, // Example static, replace with real
            'budget_spent' => BranchTransaction::where('branch_id', $branchId)->where('type', 'expenditure')->sum('amount'),
            'budget_used' => 0,
            'cash_flow' => 0,
        ];
        $summary['budget_used'] = $summary['budget_total'] > 0 ? round(($summary['budget_spent'] / $summary['budget_total']) * 100, 2) : 0;
        $summary['cash_flow'] = $summary['income'] - $summary['expenditure'];
        $transactions = BranchTransaction::where('branch_id', $branchId)->latest()->take(10)->get();
        $categorySummary = BranchTransaction::where('branch_id', $branchId)
            ->where('type', 'expenditure')
            ->select('category', DB::raw('SUM(amount) as total'))
            ->groupBy('category')->pluck('total', 'category')->toArray();
        $cashFlowLabels = BranchTransaction::where('branch_id', $branchId)
            ->orderBy('date')->pluck('date')->unique()->values()->toArray();
        $cashFlowData = [];
        foreach ($cashFlowLabels as $date) {
            $income = BranchTransaction::where('branch_id', $branchId)->where('type', 'income')->where('date', $date)->sum('amount');
            $expenditure = BranchTransaction::where('branch_id', $branchId)->where('type', 'expenditure')->where('date', $date)->sum('amount');
            $cashFlowData[] = $income - $expenditure;
        }
        $pendingApprovals = BranchTransaction::where('branch_id', $branchId)->where('approval_status', 'pending')->get();
        return view('finance.dashboard', compact('summary', 'transactions', 'categorySummary', 'cashFlowLabels', 'cashFlowData', 'pendingApprovals'));
    }

    // Show transaction entry form
    public function create()
    {
        $categories = ['Office Supplies', 'Utilities', 'Salaries', 'Loan Disbursement', 'Miscellaneous'];
        return view('finance.transaction_form', compact('categories'));
    }

    // Store new transaction
    public function store(Request $request)
    {
        $request->validate([
            'date' => 'required|date',
            'amount' => 'required|numeric|min:0.01',
            'type' => 'required|in:income,expenditure',
            'category' => 'required|string',
            'description' => 'nullable|string',
            'approval_status' => 'required|in:pending,approved,rejected',
        ]);
        BranchTransaction::create([
            'branch_id' => Auth::user()->branch_id,
            'date' => $request->date,
            'amount' => $request->amount,
            'type' => $request->type,
            'category' => $request->category,
            'description' => $request->description,
            'approval_status' => $request->approval_status,
        ]);
        return redirect()->route('finance.dashboard')->with('success', 'Transaction saved.');
    }

    // Approve a transaction
    public function approve(BranchTransaction $transaction)
    {
        $transaction->approval_status = 'approved';
        $transaction->approved_by = Auth::id();
        $transaction->approved_at = now();
        $transaction->save();
        return back()->with('success', 'Transaction approved.');
    }

    // Reject a transaction
    public function reject(BranchTransaction $transaction)
    {
        $transaction->approval_status = 'rejected';
        $transaction->approved_by = Auth::id();
        $transaction->approved_at = now();
        $transaction->save();
        return back()->with('success', 'Transaction rejected.');
    }

    // Financial reports and analytics
    public function report(Request $request)
    {
        $branchId = Auth::user()->branch_id;
        $from = $request->input('from');
        $to = $request->input('to');
        $query = BranchTransaction::where('branch_id', $branchId);
        if ($from) $query->where('date', '>=', $from);
        if ($to) $query->where('date', '<=', $to);
        $transactions = $query->orderBy('date')->get();
        // Add analytics as needed
        return view('finance.report', compact('transactions', 'from', 'to'));
    }
} 