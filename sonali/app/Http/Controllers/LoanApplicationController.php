<?php

namespace App\Http\Controllers;

use App\Models\LoanApplication;
use App\Models\Loan;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Notification;
use App\Notifications\LoanApplicationStatusChanged;

class LoanApplicationController extends Controller
{
    // List all pending/reviewable applications
    public function index(Request $request)
    {
        $query = LoanApplication::query();
        if ($request->filled('q')) {
            $query->whereHas('member', function ($q) use ($request) {
                $q->where('name', 'like', '%' . $request->q . '%')
                  ->orWhere('member_id', 'like', '%' . $request->q . '%');
            });
        }
        $application = $query->where('status', 'pending')->first();
        return view('loans.review', compact('application'));
    }

    // Approve a loan application
    public function approve(Request $request, LoanApplication $application)
    {
        $application->status = 'approved';
        $application->reviewed_by = Auth::id();
        $application->reviewed_at = now();
        $application->audit_trail = array_merge($application->audit_trail ?? [], [
            now()->toDateTimeString() . ' - Approved by ' . Auth::user()->name,
        ]);
        $application->save();
        // Optionally create Loan record here
        // Notification::send($application->member->user, new LoanApplicationStatusChanged($application));
        return back()->with('success', 'Loan application approved.');
    }

    // Reject a loan application
    public function reject(Request $request, LoanApplication $application)
    {
        $application->status = 'rejected';
        $application->reviewed_by = Auth::id();
        $application->reviewed_at = now();
        $application->audit_trail = array_merge($application->audit_trail ?? [], [
            now()->toDateTimeString() . ' - Rejected by ' . Auth::user()->name,
        ]);
        $application->save();
        // Notification::send($application->member->user, new LoanApplicationStatusChanged($application));
        return back()->with('success', 'Loan application rejected.');
    }

    // Upload documentation
    public function uploadDocs(Request $request, LoanApplication $application)
    {
        $request->validate([
            'docs.*' => 'file|max:4096',
        ]);
        $docs = $application->documents ?? [];
        foreach ($request->file('docs', []) as $file) {
            $path = $file->store('loan_docs', 'public');
            $docs[] = $path;
        }
        $application->documents = $docs;
        $application->audit_trail = array_merge($application->audit_trail ?? [], [
            now()->toDateTimeString() . ' - Documents uploaded by ' . Auth::user()->name,
        ]);
        $application->save();
        return back()->with('success', 'Documents uploaded.');
    }

    // Bulk approve applications
    public function bulkApprove(Request $request)
    {
        $ids = $request->input('ids', []);
        $applications = LoanApplication::whereIn('id', $ids)->where('status', 'pending')->get();
        foreach ($applications as $application) {
            $application->status = 'approved';
            $application->reviewed_by = Auth::id();
            $application->reviewed_at = now();
            $application->audit_trail = array_merge($application->audit_trail ?? [], [
                now()->toDateTimeString() . ' - Bulk approved by ' . Auth::user()->name,
            ]);
            $application->save();
        }
        return back()->with('success', 'Selected applications approved.');
    }
} 