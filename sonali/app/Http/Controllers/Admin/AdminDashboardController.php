<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Branch;
use App\Models\Loan;
use Illuminate\Support\Facades\DB;

class AdminDashboardController extends Controller
{
    public function index()
    {
        $stats = [
            'users' => User::count(),
            'branches' => Branch::count(),
            'loans' => Loan::count(),
            'system_health' => 'Healthy',
            'api_status' => 'Online',
            'db_status' => 'Online',
            'queue_status' => 'Online',
            'cache_status' => 'Online',
        ];
        $branchPerformance = Branch::withCount('members')->get()->pluck('members_count', 'name')->toArray();
        $userSummary = [
            'admins' => User::where('role', 'admin')->count(),
            'managers' => User::where('role', 'manager')->count(),
            'officers' => User::where('role', 'officer')->count(),
            'members' => User::where('role', 'member')->count(),
        ];
        $biReportLabels = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'];
        $biReportData = [120, 150, 170, 140, 180, 200]; // Example
        $kpiLabels = ['Growth', 'Retention', 'Profit', 'Risk', 'Satisfaction'];
        $kpiData = [80, 70, 90, 60, 85]; // Example
        return view('admin.dashboard', compact('stats', 'branchPerformance', 'userSummary', 'biReportLabels', 'biReportData', 'kpiLabels', 'kpiData'));
    }
} 