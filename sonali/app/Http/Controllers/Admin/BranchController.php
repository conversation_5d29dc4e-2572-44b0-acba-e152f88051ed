<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Branch;
use App\Models\User;
use Illuminate\Http\Request;

class BranchController extends Controller
{
    public function index(Request $request)
    {
        $query = Branch::query();
        if ($request->filled('q')) {
            $query->where('name', 'like', '%' . $request->q . '%')
                  ->orWhere('code', 'like', '%' . $request->q . '%');
        }
        $branches = $query->with('manager')->paginate(20);
        return view('admin.branches.index', compact('branches'));
    }

    public function create()
    {
        $managers = User::where('role', 'manager')->get();
        return view('admin.branches.create', compact('managers'));
    }

    public function store(Request $request)
    {
        $data = $request->validate([
            'name' => 'required|string|max:255',
            'code' => 'required|string|max:20|unique:branches,code',
            'address' => 'required|string',
            'manager_id' => 'nullable|exists:users,id',
        ]);
        Branch::create($data);
        return redirect()->route('admin.branches.index')->with('success', 'Branch created.');
    }

    public function edit(Branch $branch)
    {
        $managers = User::where('role', 'manager')->get();
        return view('admin.branches.edit', compact('branch', 'managers'));
    }

    public function update(Request $request, Branch $branch)
    {
        $data = $request->validate([
            'name' => 'required|string|max:255',
            'code' => 'required|string|max:20|unique:branches,code,' . $branch->id,
            'address' => 'required|string',
            'manager_id' => 'nullable|exists:users,id',
        ]);
        $branch->update($data);
        return redirect()->route('admin.branches.index')->with('success', 'Branch updated.');
    }

    public function destroy(Branch $branch)
    {
        $branch->delete();
        return back()->with('success', 'Branch deleted.');
    }

    public function assignManager(Request $request, Branch $branch)
    {
        $managers = User::where('role', 'manager')->get();
        if ($request->isMethod('post')) {
            $request->validate(['manager_id' => 'required|exists:users,id']);
            $branch->manager_id = $request->manager_id;
            $branch->save();
            return redirect()->route('admin.branches.index')->with('success', 'Manager assigned.');
        }
        return view('admin.branches.assign_manager', compact('branch', 'managers'));
    }

    public function users(Branch $branch)
    {
        $users = $branch->users()->paginate(20);
        return view('admin.branches.users', compact('branch', 'users'));
    }
} 