<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Support\Facades\Auth;

class RolePermissionMiddleware
{
    public function handle($request, Closure $next, $role = null, $permission = null)
    {
        $user = Auth::user();
        if ($role && !$user->hasRole($role)) {
            abort(403, 'Unauthorized (role)');
        }
        if ($permission && !$user->can($permission)) {
            abort(403, 'Unauthorized (permission)');
        }
        return $next($request);
    }
} 