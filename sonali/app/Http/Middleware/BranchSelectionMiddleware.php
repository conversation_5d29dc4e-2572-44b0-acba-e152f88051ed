<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class BranchSelectionMiddleware
{
    public function handle(Request $request, Closure $next)
    {
        $user = Auth::user();
        if ($user && !$user->branch_id && !$user->hasRole('admin')) {
            return redirect()->route('branch.select');
        }
        return $next($request);
    }
} 