<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreMemberRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'name' => 'required|string|max:255',
            'father_or_husband_name' => 'nullable|string|max:255',
            'mother_name' => 'nullable|string|max:255',
            'present_address' => 'nullable|string|max:255',
            'permanent_address' => 'nullable|string|max:255',
            'nid_number' => 'required|digits_between:10,17|unique:members,nid_number',
            'date_of_birth' => 'required|date|before:-18 years',
            'religion' => 'nullable|string|max:50',
            'phone_number' => 'required|regex:/^01[3-9]\d{8}$/|unique:members,phone_number',
            'blood_group' => 'nullable|in:A+,A-,B+,B-,O+,O-,AB+,AB-',
            'photo' => 'nullable|image|mimes:jpeg,png,webp|max:2048',
            'occupation' => 'nullable|string|max:100',
            'reference_id' => 'nullable|exists:members,id',
            'branch_id' => 'required|exists:branches,id',
            'created_by' => 'required|exists:users,id',
            'kyc_status' => 'nullable|in:pending,approved,rejected',
            'kyc_documents_path' => 'nullable|string|max:255',
        ];
    }
} 