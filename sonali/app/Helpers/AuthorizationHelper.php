<?php

namespace App\Helpers;

use Illuminate\Support\Facades\Auth;

class AuthorizationHelper
{
    public static function hasRole($role)
    {
        return Auth::check() && Auth::user()->hasRole($role);
    }

    public static function hasPermission($permission)
    {
        return Auth::check() && Auth::user()->can($permission);
    }

    public static function canAccessBranch($branchId)
    {
        $user = Auth::user();
        return $user && ($user->branch_id === $branchId || $user->hasRole('admin'));
    }
} 