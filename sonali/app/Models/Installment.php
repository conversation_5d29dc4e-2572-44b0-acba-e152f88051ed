<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Installment extends Model
{
    use HasFactory;

    protected $fillable = [
        'loan_id', 'installment_no', 'installment_date', 'installment_amount', 'advance_paid', 'due_amount', 'collected_by', 'collection_date', 'status',
    ];

    public function loan()
    {
        return $this->belongsTo(Loan::class);
    }

    public function collector()
    {
        return $this->belongsTo(User::class, 'collected_by');
    }
} 