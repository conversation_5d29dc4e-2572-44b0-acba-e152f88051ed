<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Loan extends Model
{
    use HasFactory;

    protected $fillable = [
        'loan_application_id', 'loan_date', 'loan_amount', 'total_repayment_amount', 'repayment_duration', 'repayment_method', 'installment_count', 'installment_amount', 'advance_payment', 'first_installment_date', 'last_installment_date', 'disbursement_status',
    ];

    public function application()
    {
        return $this->belongsTo(LoanApplication::class, 'loan_application_id');
    }

    public function installments()
    {
        return $this->hasMany(Installment::class);
    }

    public function generateInstallments()
    {
        $installments = [];
        $date = \Carbon\Carbon::parse($this->first_installment_date);
        for ($i = 1; $i <= $this->installment_count; $i++) {
            $installments[] = [
                'loan_id' => $this->id,
                'installment_no' => $i,
                'installment_date' => $date->copy()->addMonths($i - 1),
                'installment_amount' => $this->installment_amount,
                'advance_paid' => 0,
                'due_amount' => $this->installment_amount,
                'status' => 'pending',
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }
        return $installments;
    }

    public function overdueInstallments()
    {
        return $this->installments()->where('status', 'pending')->where('installment_date', '<', now());
    }

    // Example calculation methods
    public function calculateInstallmentAmount(): float
    {
        if ($this->installment_count > 0) {
            return round($this->total_repayment_amount / $this->installment_count, 2);
        }
        return 0;
    }

    public function calculateTotalRepaymentAmount(): float
    {
        return $this->installment_count * $this->installment_amount;
    }
} 