<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SavingAccount extends Model
{
    use HasFactory;

    protected $fillable = [
        'member_id', 'saving_type', 'joint_photo', 'nominee_name', 'nominee_relation', 'saving_method', 'monthly_amount', 'fdr_amount', 'start_date', 'created_by',
    ];

    public function member()
    {
        return $this->belongsTo(Member::class);
    }

    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    // Example logic for different saving types
    public function isGeneral()
    {
        return $this->saving_type === 'general';
    }
    public function isDps()
    {
        return $this->saving_type === 'dps';
    }
    public function isFdr()
    {
        return $this->saving_type === 'fdr';
    }

    // Example calculation method
    public function calculateTotalSavings()
    {
        if ($this->isGeneral() || $this->isDps()) {
            $months = now()->diffInMonths($this->start_date);
            return $months * $this->monthly_amount;
        } elseif ($this->isFdr()) {
            return $this->fdr_amount;
        }
        return 0;
    }
} 