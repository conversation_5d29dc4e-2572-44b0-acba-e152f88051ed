<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class BranchTransaction extends Model
{
    use HasFactory;

    protected $fillable = [
        'branch_id', 'date', 'amount', 'type', 'category', 'description', 'approval_status', 'approved_by', 'approved_at',
    ];

    protected $dates = ['date', 'approved_at'];

    public function branch()
    {
        return $this->belongsTo(Branch::class);
    }

    public function approver()
    {
        return $this->belongsTo(User::class, 'approved_by');
    }
} 