<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class LoanApplication extends Model
{
    use HasFactory;

    protected $fillable = [
        'member_id', 'applied_amount', 'reason', 'loan_cycle_number', 'recommender_id', 'status', 'advance_payment', 'reviewed_by', 'reviewed_at', 'applied_at',
    ];

    protected $casts = [
        'documents' => 'array',
        'audit_trail' => 'array',
    ];

    public function member()
    {
        return $this->belongsTo(Member::class);
    }

    public function recommender()
    {
        return $this->belongsTo(Member::class, 'recommender_id');
    }

    public function reviewer()
    {
        return $this->belongsTo(User::class, 'reviewed_by');
    }

    public function loan()
    {
        return $this->hasOne(Loan::class);
    }
} 