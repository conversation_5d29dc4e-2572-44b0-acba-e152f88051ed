<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Support\Facades\Crypt;

class Member extends Model
{
    use HasFactory;

    protected $fillable = [
        'member_id', 'name', 'father_or_husband_name', 'mother_name', 'present_address', 'permanent_address',
        'nid_number', 'date_of_birth', 'religion', 'phone_number', 'blood_group', 'photo', 'occupation',
        'reference_id', 'branch_id', 'created_by', 'kyc_status', 'kyc_documents_path',
    ];

    public function branch()
    {
        return $this->belongsTo(Branch::class);
    }

    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function reference()
    {
        return $this->belongsTo(Member::class, 'reference_id');
    }

    public function references()
    {
        return $this->hasMany(Member::class, 'reference_id');
    }

    protected function nidNumber(): Attribute
    {
        return Attribute::make(
            get: fn ($value) => Crypt::decryptString($value),
            set: fn ($value) => Crypt::encryptString($value),
        );
    }
} 