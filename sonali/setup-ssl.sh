#!/bin/bash

# SSL Certificate Setup Script for Sonali Bank
# This script sets up SSL certificates using Let's Encrypt

set -e

echo "🔒 Setting up SSL certificates for www.sonalibd.org..."

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as root
if [[ $EUID -ne 0 ]]; then
   print_error "This script must be run as root (use sudo)"
   exit 1
fi

# Check if domain is pointing to this server
print_status "Checking DNS configuration..."
DOMAIN_IP=$(dig +short www.sonalibd.org)
SERVER_IP=$(curl -s ifconfig.me)

if [ "$DOMAIN_IP" != "$SERVER_IP" ]; then
    print_warning "Domain www.sonalibd.org does not point to this server"
    print_warning "Domain IP: $DOMAIN_IP"
    print_warning "Server IP: $SERVER_IP"
    print_warning "Please update your DNS records before continuing"
    read -p "Continue anyway? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

# Install certbot if not already installed
print_status "Installing certbot..."
apt update
apt install -y certbot python3-certbot-nginx

# Stop nginx temporarily
print_status "Stopping nginx temporarily..."
systemctl stop nginx

# Obtain SSL certificate
print_status "Obtaining SSL certificate from Let's Encrypt..."
certbot certonly --standalone \
    --email <EMAIL> \
    --agree-tos \
    --no-eff-email \
    -d sonalibd.org \
    -d www.sonalibd.org

# Update nginx configuration with correct certificate paths
print_status "Updating nginx configuration..."
sed -i 's|/etc/ssl/certs/sonalibd.org.crt|/etc/letsencrypt/live/sonalibd.org/fullchain.pem|g' /etc/nginx/sites-available/sonalibd.org
sed -i 's|/etc/ssl/private/sonalibd.org.key|/etc/letsencrypt/live/sonalibd.org/privkey.pem|g' /etc/nginx/sites-available/sonalibd.org

# Test nginx configuration
print_status "Testing nginx configuration..."
nginx -t

# Start nginx
print_status "Starting nginx..."
systemctl start nginx

# Set up automatic renewal
print_status "Setting up automatic certificate renewal..."
(crontab -l 2>/dev/null; echo "0 12 * * * /usr/bin/certbot renew --quiet --nginx") | crontab -

# Test certificate
print_status "Testing SSL certificate..."
curl -I https://www.sonalibd.org || print_warning "SSL test failed - please check manually"

print_status "✅ SSL certificate setup completed!"
print_status "🌐 Your website is now accessible at: https://www.sonalibd.org"
print_warning "Certificate will auto-renew. Check renewal with: certbot renew --dry-run"
