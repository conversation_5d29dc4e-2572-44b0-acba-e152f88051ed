<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Auth\LoginController;
use App\Http\Controllers\LoanApplicationController;
use App\Http\Controllers\BranchTransactionController;
use App\Http\Controllers\Admin\AdminDashboardController;
use App\Http\Controllers\Admin\UserController;
use App\Http\Controllers\Admin\BranchController;
use App\Http\Controllers\HealthController;

Route::get('/', function () {
    return view('welcome');
});

// Health check routes
Route::get('/health', [HealthController::class, 'index'])->name('health.index');
Route::get('/health/database', [HealthController::class, 'database'])->name('health.database');
Route::get('/health/cache', [HealthController::class, 'cache'])->name('health.cache');
Route::get('/health/simple', [HealthController::class, 'simple'])->name('health.simple');

Route::get('login', [LoginController::class, 'showLoginForm'])->name('login');
Route::post('login', [LoginController::class, 'login']);
Route::post('logout', [LoginController::class, 'logout'])->name('logout');

Route::middleware(['auth', 'role:manager'])->group(function () {
    Route::get('/loans/review', [LoanApplicationController::class, 'index'])->name('loans.review');
    Route::post('/loans/{application}/approve', [LoanApplicationController::class, 'approve'])->name('loans.approve');
    Route::post('/loans/{application}/reject', [LoanApplicationController::class, 'reject'])->name('loans.reject');
    Route::post('/loans/{application}/upload-docs', [LoanApplicationController::class, 'uploadDocs'])->name('loans.uploadDocs');
    Route::post('/loans/bulk-approve', [LoanApplicationController::class, 'bulkApprove'])->name('loans.bulkApprove');
    Route::get('/finance/dashboard', [BranchTransactionController::class, 'dashboard'])->name('finance.dashboard');
    Route::get('/finance/transactions/create', [BranchTransactionController::class, 'create'])->name('finance.transactions.create');
    Route::post('/finance/transactions', [BranchTransactionController::class, 'store'])->name('finance.transactions.store');
    Route::post('/finance/transactions/{transaction}/approve', [BranchTransactionController::class, 'approve'])->name('finance.transactions.approve');
    Route::post('/finance/transactions/{transaction}/reject', [BranchTransactionController::class, 'reject'])->name('finance.transactions.reject');
    Route::get('/finance/report', [BranchTransactionController::class, 'report'])->name('finance.report');
});

Route::middleware(['auth', 'role:admin'])->group(function () {
    Route::get('/admin/dashboard', [AdminDashboardController::class, 'index'])->name('admin.dashboard');
    // User management
    Route::get('/admin/users', [UserController::class, 'index'])->name('admin.users.index');
    Route::get('/admin/users/{user}', [UserController::class, 'show'])->name('admin.users.show');
    Route::get('/admin/users/{user}/edit', [UserController::class, 'edit'])->name('admin.users.edit');
    Route::put('/admin/users/{user}', [UserController::class, 'update'])->name('admin.users.update');
    Route::delete('/admin/users/{user}', [UserController::class, 'destroy'])->name('admin.users.destroy');
    Route::match(['get','post'],'/admin/users/{user}/assign-role', [UserController::class, 'assignRole'])->name('admin.users.assignRole');
    Route::post('/admin/users/bulk', [UserController::class, 'bulk'])->name('admin.users.bulk');
    Route::get('/admin/users/{user}/activity', [UserController::class, 'activity'])->name('admin.users.activity');
    Route::get('/admin/users/{user}/auth-logs', [UserController::class, 'authLogs'])->name('admin.users.authLogs');
    // Branch management
    Route::get('/admin/branches', [BranchController::class, 'index'])->name('admin.branches.index');
    Route::get('/admin/branches/create', [BranchController::class, 'create'])->name('admin.branches.create');
    Route::post('/admin/branches', [BranchController::class, 'store'])->name('admin.branches.store');
    Route::get('/admin/branches/{branch}/edit', [BranchController::class, 'edit'])->name('admin.branches.edit');
    Route::put('/admin/branches/{branch}', [BranchController::class, 'update'])->name('admin.branches.update');
    Route::delete('/admin/branches/{branch}', [BranchController::class, 'destroy'])->name('admin.branches.destroy');
    Route::match(['get','post'],'/admin/branches/{branch}/assign-manager', [BranchController::class, 'assignManager'])->name('admin.branches.assignManager');
    Route::get('/admin/branches/{branch}/users', [BranchController::class, 'users'])->name('admin.branches.users');
});
