# Sonali Bank Website Deployment Guide

This guide will help you deploy the Sonali Bank Laravel application to www.sonalibd.org.

## Prerequisites

- Ubuntu 20.04+ or similar Linux server
- Root or sudo access
- Domain name (sonalibd.org) pointing to your server
- At least 2GB RAM and 20GB storage

## Quick Deployment

### 1. Fix Database Configuration Issue

The health check function has been removed from `config/database.php` as recommended. A proper health check service has been implemented instead.

### 2. Run the Deployment Script

```bash
# Make scripts executable
chmod +x deploy.sh
chmod +x optimize-production.sh
chmod +x setup-ssl.sh

# Run the main deployment script
./deploy.sh
```

### 3. Configure Database

```bash
# Login to MySQL as root
sudo mysql -u root -p

# Run the database setup script
source setup-database.sql

# Update the database password in .env file
nano .env
# Change DB_PASSWORD to your actual strong password
```

### 4. Set up SSL Certificate

```bash
# Run SSL setup (after DNS is configured)
sudo ./setup-ssl.sh
```

### 5. Optimize for Production

```bash
# Run production optimization
./optimize-production.sh
```

## Manual Steps

### 1. Server Setup

```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install required packages
sudo apt install -y nginx mysql-server redis-server php8.2-fpm php8.2-mysql php8.2-redis php8.2-xml php8.2-curl php8.2-mbstring php8.2-zip php8.2-gd php8.2-intl php8.2-bcmath composer nodejs npm
```

### 2. Configure Nginx

```bash
# Copy nginx configuration
sudo cp nginx-sonalibd.conf /etc/nginx/sites-available/sonalibd.org
sudo ln -s /etc/nginx/sites-available/sonalibd.org /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl restart nginx
```

### 3. Configure Database

```bash
# Secure MySQL installation
sudo mysql_secure_installation

# Create database and user
sudo mysql -u root -p < setup-database.sql
```

### 4. Configure Application

```bash
# Copy production environment
cp .env.production .env

# Update database credentials
nano .env

# Install dependencies
composer install --no-dev --optimize-autoloader
npm install && npm run build

# Set up Laravel
php artisan key:generate
php artisan migrate --force
php artisan storage:link
```

### 5. Set Permissions

```bash
sudo chown -R www-data:www-data /var/www/sonali
sudo chmod -R 755 /var/www/sonali
sudo chmod -R 775 /var/www/sonali/storage
sudo chmod -R 775 /var/www/sonali/bootstrap/cache
```

## Configuration Files

### Environment Variables (.env)

Key settings for production:
- `APP_ENV=production`
- `APP_DEBUG=false`
- `APP_URL=https://www.sonalibd.org`
- Database credentials
- Mail configuration
- Session security settings

### Nginx Configuration

- SSL/TLS encryption
- Security headers
- Gzip compression
- Static file caching
- PHP-FPM integration

## Health Monitoring

The application includes health check endpoints:

- `/health` - Overall system health
- `/health/database` - Database connectivity
- `/health/cache` - Cache system status
- `/up` - Laravel's built-in health check

## Security Features

- SSL/TLS encryption
- Security headers
- Session encryption
- CSRF protection
- Input validation
- File upload restrictions

## Maintenance

### Regular Tasks

```bash
# Update application
git pull origin main
composer install --no-dev --optimize-autoloader
npm run build
php artisan migrate --force
./optimize-production.sh

# Backup database
php artisan db:backup

# Monitor logs
tail -f storage/logs/laravel.log
```

### SSL Certificate Renewal

Certificates auto-renew via cron job. Test renewal:

```bash
sudo certbot renew --dry-run
```

## Troubleshooting

### Common Issues

1. **Permission errors**: Run permission script
2. **Database connection**: Check credentials in .env
3. **SSL issues**: Verify DNS and certificate paths
4. **Cache issues**: Clear all caches and rebuild

### Log Files

- Application: `storage/logs/laravel.log`
- Nginx: `/var/log/nginx/sonalibd.org.error.log`
- PHP-FPM: `/var/log/php8.2-fpm.log`

## Support

For technical support, check:
1. Application logs
2. Server logs
3. Health check endpoints
4. System resource usage

## Post-Deployment Checklist

- [ ] Website loads at https://www.sonalibd.org
- [ ] SSL certificate is valid
- [ ] Database connection works
- [ ] Login functionality works
- [ ] File uploads work
- [ ] Email sending works
- [ ] Health checks pass
- [ ] Backups are configured
- [ ] Monitoring is set up
